const googleSheetsService = require('../services/googleSheets.service');

class CompanyController {
  async listCompanies(req, res) {
    try {
      const companies = await googleSheetsService.getCompanyData();
      res.json(companies);
    } catch (error) {
      console.error('Error listing companies:', error);
      res.status(500).json({ error: error.message });
    }
  }

  async getCompany(req, res) {
    try {
      const { companyName } = req.params;
      const company = await googleSheetsService.getCompanyByName(companyName);
      
      if (!company) {
        return res.status(404).json({ error: 'Company not found' });
      }
      
      res.json(company);
    } catch (error) {
      console.error('Error getting company:', error);
      res.status(500).json({ error: error.message });
    }
  }
}

module.exports = new CompanyController();