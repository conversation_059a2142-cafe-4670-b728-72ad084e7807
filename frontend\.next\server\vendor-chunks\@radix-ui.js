"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/number/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@radix-ui/number/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ clamp)\n/* harmony export */ });\n// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL251bWJlci9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJEOlxcYmxvZy1nZW4tYWlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEByYWRpeC11aVxcbnVtYmVyXFxkaXN0XFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvY29yZS9udW1iZXIvc3JjL251bWJlci50c1xuZnVuY3Rpb24gY2xhbXAodmFsdWUsIFttaW4sIG1heF0pIHtcbiAgcmV0dXJuIE1hdGgubWluKG1heCwgTWF0aC5tYXgobWluLCB2YWx1ZSkpO1xufVxuZXhwb3J0IHtcbiAgY2xhbXBcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1RUFBdUUsa0NBQWtDLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJEOlxcYmxvZy1nZW4tYWlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEByYWRpeC11aVxccHJpbWl0aXZlXFxkaXN0XFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvY29yZS9wcmltaXRpdmUvc3JjL3ByaW1pdGl2ZS50c3hcbmZ1bmN0aW9uIGNvbXBvc2VFdmVudEhhbmRsZXJzKG9yaWdpbmFsRXZlbnRIYW5kbGVyLCBvdXJFdmVudEhhbmRsZXIsIHsgY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID0gdHJ1ZSB9ID0ge30pIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIGhhbmRsZUV2ZW50KGV2ZW50KSB7XG4gICAgb3JpZ2luYWxFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgaWYgKGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9PT0gZmFsc2UgfHwgIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHtcbiAgICAgIHJldHVybiBvdXJFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgfVxuICB9O1xufVxuZXhwb3J0IHtcbiAgY29tcG9zZUV2ZW50SGFuZGxlcnNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-arrow/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/arrow/src/Arrow.tsx\n\n\n\nvar NAME = \"Arrow\";\nvar Arrow = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.svg,\n    {\n      ...arrowProps,\n      ref: forwardedRef,\n      width,\n      height,\n      viewBox: \"0 0 30 10\",\n      preserveAspectRatio: \"none\",\n      children: props.asChild ? children : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"polygon\", { points: \"0,0 30,0 15,10\" })\n    }\n  );\n});\nArrow.displayName = NAME;\nvar Root = Arrow;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWFycm93L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDK0I7QUFDdUI7QUFDZDtBQUN4QztBQUNBLFlBQVksNkNBQWdCO0FBQzVCLFVBQVUsa0RBQWtEO0FBQzVELHlCQUF5QixzREFBRztBQUM1QixJQUFJLGdFQUFTO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQsc0RBQUcsY0FBYywwQkFBMEI7QUFDdEc7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBSUU7QUFDRiIsInNvdXJjZXMiOlsiRDpcXGJsb2ctZ2VuLWFpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHJlYWN0LWFycm93XFxkaXN0XFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvYXJyb3cvc3JjL0Fycm93LnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBQcmltaXRpdmUgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZVwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgTkFNRSA9IFwiQXJyb3dcIjtcbnZhciBBcnJvdyA9IFJlYWN0LmZvcndhcmRSZWYoKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgY29uc3QgeyBjaGlsZHJlbiwgd2lkdGggPSAxMCwgaGVpZ2h0ID0gNSwgLi4uYXJyb3dQcm9wcyB9ID0gcHJvcHM7XG4gIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgIFByaW1pdGl2ZS5zdmcsXG4gICAge1xuICAgICAgLi4uYXJyb3dQcm9wcyxcbiAgICAgIHJlZjogZm9yd2FyZGVkUmVmLFxuICAgICAgd2lkdGgsXG4gICAgICBoZWlnaHQsXG4gICAgICB2aWV3Qm94OiBcIjAgMCAzMCAxMFwiLFxuICAgICAgcHJlc2VydmVBc3BlY3RSYXRpbzogXCJub25lXCIsXG4gICAgICBjaGlsZHJlbjogcHJvcHMuYXNDaGlsZCA/IGNoaWxkcmVuIDogLyogQF9fUFVSRV9fICovIGpzeChcInBvbHlnb25cIiwgeyBwb2ludHM6IFwiMCwwIDMwLDAgMTUsMTBcIiB9KVxuICAgIH1cbiAgKTtcbn0pO1xuQXJyb3cuZGlzcGxheU5hbWUgPSBOQU1FO1xudmFyIFJvb3QgPSBBcnJvdztcbmV4cG9ydCB7XG4gIEFycm93LFxuICBSb290XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ createCollection auto */ // packages/react/collection/src/Collection.tsx\n\n\n\n\n\nfunction createCollection(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionRef: {\n            current: null\n        },\n        itemMap: /* @__PURE__ */ new Map()\n    });\n    const CollectionProvider = (props)=>{\n        const { scope, children } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            scope,\n            itemMap,\n            collectionRef: ref,\n            children\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n            \"createCollection.CollectionItemSlot.useEffect\": ()=>{\n                context.itemMap.set(ref, {\n                    ref,\n                    ...itemData\n                });\n                return ({\n                    \"createCollection.CollectionItemSlot.useEffect\": ()=>void context.itemMap.delete(ref)\n                })[\"createCollection.CollectionItemSlot.useEffect\"];\n            }\n        }[\"createCollection.CollectionItemSlot.useEffect\"]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useCollection(scope) {\n        const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n        const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"createCollection.useCollection.useCallback[getItems]\": ()=>{\n                const collectionNode = context.collectionRef.current;\n                if (!collectionNode) return [];\n                const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n                const items = Array.from(context.itemMap.values());\n                const orderedItems = items.sort({\n                    \"createCollection.useCollection.useCallback[getItems].orderedItems\": (a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current)\n                }[\"createCollection.useCollection.useCallback[getItems].orderedItems\"]);\n                return orderedItems;\n            }\n        }[\"createCollection.useCollection.useCallback[getItems]\"], [\n            context.collectionRef,\n            context.itemMap\n        ]);\n        return getItems;\n    }\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        useCollection,\n        createCollectionScope\n    ];\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/composeRefs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/createContext.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-direction/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectionProvider: () => (/* binding */ DirectionProvider),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useDirection: () => (/* binding */ useDirection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/direction/src/Direction.tsx\n\n\nvar DirectionContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQytCO0FBQ1M7QUFDeEMsdUJBQXVCLGdEQUFtQjtBQUMxQztBQUNBLFVBQVUsZ0JBQWdCO0FBQzFCLHlCQUF5QixzREFBRyw4QkFBOEIsc0JBQXNCO0FBQ2hGO0FBQ0E7QUFDQSxvQkFBb0IsNkNBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUtFO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxibG9nLWdlbi1haVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQHJhZGl4LXVpXFxyZWFjdC1kaXJlY3Rpb25cXGRpc3RcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9kaXJlY3Rpb24vc3JjL0RpcmVjdGlvbi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgRGlyZWN0aW9uQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQodm9pZCAwKTtcbnZhciBEaXJlY3Rpb25Qcm92aWRlciA9IChwcm9wcykgPT4ge1xuICBjb25zdCB7IGRpciwgY2hpbGRyZW4gfSA9IHByb3BzO1xuICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChEaXJlY3Rpb25Db250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlOiBkaXIsIGNoaWxkcmVuIH0pO1xufTtcbmZ1bmN0aW9uIHVzZURpcmVjdGlvbihsb2NhbERpcikge1xuICBjb25zdCBnbG9iYWxEaXIgPSBSZWFjdC51c2VDb250ZXh0KERpcmVjdGlvbkNvbnRleHQpO1xuICByZXR1cm4gbG9jYWxEaXIgfHwgZ2xvYmFsRGlyIHx8IFwibHRyXCI7XG59XG52YXIgUHJvdmlkZXIgPSBEaXJlY3Rpb25Qcm92aWRlcjtcbmV4cG9ydCB7XG4gIERpcmVjdGlvblByb3ZpZGVyLFxuICBQcm92aWRlcixcbiAgdXNlRGlyZWN0aW9uXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // packages/react/dismissable-layer/src/DismissableLayer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, {\n        \"DismissableLayer.useComposedRefs[composedRefs]\": (node2)=>setNode(node2)\n    }[\"DismissableLayer.useComposedRefs[composedRefs]\"]);\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside({\n        \"DismissableLayer.usePointerDownOutside[pointerDownOutside]\": (event)=>{\n            const target = event.target;\n            const isPointerDownOnBranch = [\n                ...context.branches\n            ].some({\n                \"DismissableLayer.usePointerDownOutside[pointerDownOutside].isPointerDownOnBranch\": (branch)=>branch.contains(target)\n            }[\"DismissableLayer.usePointerDownOutside[pointerDownOutside].isPointerDownOnBranch\"]);\n            if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n            onPointerDownOutside?.(event);\n            onInteractOutside?.(event);\n            if (!event.defaultPrevented) onDismiss?.();\n        }\n    }[\"DismissableLayer.usePointerDownOutside[pointerDownOutside]\"], ownerDocument);\n    const focusOutside = useFocusOutside({\n        \"DismissableLayer.useFocusOutside[focusOutside]\": (event)=>{\n            const target = event.target;\n            const isFocusInBranch = [\n                ...context.branches\n            ].some({\n                \"DismissableLayer.useFocusOutside[focusOutside].isFocusInBranch\": (branch)=>branch.contains(target)\n            }[\"DismissableLayer.useFocusOutside[focusOutside].isFocusInBranch\"]);\n            if (isFocusInBranch) return;\n            onFocusOutside?.(event);\n            onInteractOutside?.(event);\n            if (!event.defaultPrevented) onDismiss?.();\n        }\n    }[\"DismissableLayer.useFocusOutside[focusOutside]\"], ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)({\n        \"DismissableLayer.useEscapeKeydown\": (event)=>{\n            const isHighestLayer = index === context.layers.size - 1;\n            if (!isHighestLayer) return;\n            onEscapeKeyDown?.(event);\n            if (!event.defaultPrevented && onDismiss) {\n                event.preventDefault();\n                onDismiss();\n            }\n        }\n    }[\"DismissableLayer.useEscapeKeydown\"], ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            if (!node) return;\n            if (disableOutsidePointerEvents) {\n                if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                    originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                    ownerDocument.body.style.pointerEvents = \"none\";\n                }\n                context.layersWithOutsidePointerEventsDisabled.add(node);\n            }\n            context.layers.add(node);\n            dispatchUpdate();\n            return ({\n                \"DismissableLayer.useEffect\": ()=>{\n                    if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                        ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n                    }\n                }\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            return ({\n                \"DismissableLayer.useEffect\": ()=>{\n                    if (!node) return;\n                    context.layers.delete(node);\n                    context.layersWithOutsidePointerEventsDisabled.delete(node);\n                    dispatchUpdate();\n                }\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayer.useEffect\": ()=>{\n            const handleUpdate = {\n                \"DismissableLayer.useEffect.handleUpdate\": ()=>force({})\n            }[\"DismissableLayer.useEffect.handleUpdate\"];\n            document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n            return ({\n                \"DismissableLayer.useEffect\": ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate)\n            })[\"DismissableLayer.useEffect\"];\n        }\n    }[\"DismissableLayer.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"DismissableLayerBranch.useEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                context.branches.add(node);\n                return ({\n                    \"DismissableLayerBranch.useEffect\": ()=>{\n                        context.branches.delete(node);\n                    }\n                })[\"DismissableLayerBranch.useEffect\"];\n            }\n        }\n    }[\"DismissableLayerBranch.useEffect\"], [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        \"usePointerDownOutside.useRef[handleClickRef]\": ()=>{}\n    }[\"usePointerDownOutside.useRef[handleClickRef]\"]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePointerDownOutside.useEffect\": ()=>{\n            const handlePointerDown = {\n                \"usePointerDownOutside.useEffect.handlePointerDown\": (event)=>{\n                    if (event.target && !isPointerInsideReactTreeRef.current) {\n                        let handleAndDispatchPointerDownOutsideEvent2 = {\n                            \"usePointerDownOutside.useEffect.handlePointerDown.handleAndDispatchPointerDownOutsideEvent2\": function() {\n                                handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                                    discrete: true\n                                });\n                            }\n                        }[\"usePointerDownOutside.useEffect.handlePointerDown.handleAndDispatchPointerDownOutsideEvent2\"];\n                        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                        const eventDetail = {\n                            originalEvent: event\n                        };\n                        if (event.pointerType === \"touch\") {\n                            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                            handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                            ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                                once: true\n                            });\n                        } else {\n                            handleAndDispatchPointerDownOutsideEvent2();\n                        }\n                    } else {\n                        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    }\n                    isPointerInsideReactTreeRef.current = false;\n                }\n            }[\"usePointerDownOutside.useEffect.handlePointerDown\"];\n            const timerId = window.setTimeout({\n                \"usePointerDownOutside.useEffect.timerId\": ()=>{\n                    ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n                }\n            }[\"usePointerDownOutside.useEffect.timerId\"], 0);\n            return ({\n                \"usePointerDownOutside.useEffect\": ()=>{\n                    window.clearTimeout(timerId);\n                    ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                }\n            })[\"usePointerDownOutside.useEffect\"];\n        }\n    }[\"usePointerDownOutside.useEffect\"], [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useFocusOutside.useEffect\": ()=>{\n            const handleFocus = {\n                \"useFocusOutside.useEffect.handleFocus\": (event)=>{\n                    if (event.target && !isFocusInsideReactTreeRef.current) {\n                        const eventDetail = {\n                            originalEvent: event\n                        };\n                        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                            discrete: false\n                        });\n                    }\n                }\n            }[\"useFocusOutside.useEffect.handleFocus\"];\n            ownerDocument.addEventListener(\"focusin\", handleFocus);\n            return ({\n                \"useFocusOutside.useEffect\": ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus)\n            })[\"useFocusOutside.useEffect\"];\n        }\n    }[\"useFocusOutside.useEffect\"], [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-guards/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusGuards: () => (/* binding */ FocusGuards),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   useFocusGuards: () => (/* binding */ useFocusGuards)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ FocusGuards,Root,useFocusGuards auto */ // packages/react/focus-guards/src/FocusGuards.tsx\n\nvar count = 0;\nfunction FocusGuards(props) {\n    useFocusGuards();\n    return props.children;\n}\nfunction useFocusGuards() {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useFocusGuards.useEffect\": ()=>{\n            const edgeGuards = document.querySelectorAll(\"[data-radix-focus-guard]\");\n            document.body.insertAdjacentElement(\"afterbegin\", edgeGuards[0] ?? createFocusGuard());\n            document.body.insertAdjacentElement(\"beforeend\", edgeGuards[1] ?? createFocusGuard());\n            count++;\n            return ({\n                \"useFocusGuards.useEffect\": ()=>{\n                    if (count === 1) {\n                        document.querySelectorAll(\"[data-radix-focus-guard]\").forEach({\n                            \"useFocusGuards.useEffect\": (node)=>node.remove()\n                        }[\"useFocusGuards.useEffect\"]);\n                    }\n                    count--;\n                }\n            })[\"useFocusGuards.useEffect\"];\n        }\n    }[\"useFocusGuards.useEffect\"], []);\n}\nfunction createFocusGuard() {\n    const element = document.createElement(\"span\");\n    element.setAttribute(\"data-radix-focus-guard\", \"\");\n    element.tabIndex = 0;\n    element.style.outline = \"none\";\n    element.style.opacity = \"0\";\n    element.style.position = \"fixed\";\n    element.style.pointerEvents = \"none\";\n    return element;\n}\nvar Root = FocusGuards;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-focus-scope/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusScope: () => (/* binding */ FocusScope),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ FocusScope,Root auto */ // packages/react/focus-scope/src/FocusScope.tsx\n\n\n\n\n\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { loop = false, trapped = false, onMountAutoFocus: onMountAutoFocusProp, onUnmountAutoFocus: onUnmountAutoFocusProp, ...scopeProps } = props;\n    const [container, setContainer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const onMountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onMountAutoFocusProp);\n    const onUnmountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onUnmountAutoFocusProp);\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"FocusScope.useComposedRefs[composedRefs]\": (node)=>setContainer(node)\n    }[\"FocusScope.useComposedRefs[composedRefs]\"]);\n    const focusScope = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        paused: false,\n        pause () {\n            this.paused = true;\n        },\n        resume () {\n            this.paused = false;\n        }\n    }).current;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"FocusScope.useEffect\": ()=>{\n            if (trapped) {\n                let handleFocusIn2 = {\n                    \"FocusScope.useEffect.handleFocusIn2\": function(event) {\n                        if (focusScope.paused || !container) return;\n                        const target = event.target;\n                        if (container.contains(target)) {\n                            lastFocusedElementRef.current = target;\n                        } else {\n                            focus(lastFocusedElementRef.current, {\n                                select: true\n                            });\n                        }\n                    }\n                }[\"FocusScope.useEffect.handleFocusIn2\"], handleFocusOut2 = {\n                    \"FocusScope.useEffect.handleFocusOut2\": function(event) {\n                        if (focusScope.paused || !container) return;\n                        const relatedTarget = event.relatedTarget;\n                        if (relatedTarget === null) return;\n                        if (!container.contains(relatedTarget)) {\n                            focus(lastFocusedElementRef.current, {\n                                select: true\n                            });\n                        }\n                    }\n                }[\"FocusScope.useEffect.handleFocusOut2\"], handleMutations2 = {\n                    \"FocusScope.useEffect.handleMutations2\": function(mutations) {\n                        const focusedElement = document.activeElement;\n                        if (focusedElement !== document.body) return;\n                        for (const mutation of mutations){\n                            if (mutation.removedNodes.length > 0) focus(container);\n                        }\n                    }\n                }[\"FocusScope.useEffect.handleMutations2\"];\n                var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n                document.addEventListener(\"focusin\", handleFocusIn2);\n                document.addEventListener(\"focusout\", handleFocusOut2);\n                const mutationObserver = new MutationObserver(handleMutations2);\n                if (container) mutationObserver.observe(container, {\n                    childList: true,\n                    subtree: true\n                });\n                return ({\n                    \"FocusScope.useEffect\": ()=>{\n                        document.removeEventListener(\"focusin\", handleFocusIn2);\n                        document.removeEventListener(\"focusout\", handleFocusOut2);\n                        mutationObserver.disconnect();\n                    }\n                })[\"FocusScope.useEffect\"];\n            }\n        }\n    }[\"FocusScope.useEffect\"], [\n        trapped,\n        container,\n        focusScope.paused\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"FocusScope.useEffect\": ()=>{\n            if (container) {\n                focusScopesStack.add(focusScope);\n                const previouslyFocusedElement = document.activeElement;\n                const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n                if (!hasFocusedCandidate) {\n                    const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n                    container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                    container.dispatchEvent(mountEvent);\n                    if (!mountEvent.defaultPrevented) {\n                        focusFirst(removeLinks(getTabbableCandidates(container)), {\n                            select: true\n                        });\n                        if (document.activeElement === previouslyFocusedElement) {\n                            focus(container);\n                        }\n                    }\n                }\n                return ({\n                    \"FocusScope.useEffect\": ()=>{\n                        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                        setTimeout({\n                            \"FocusScope.useEffect\": ()=>{\n                                const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n                                container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                                container.dispatchEvent(unmountEvent);\n                                if (!unmountEvent.defaultPrevented) {\n                                    focus(previouslyFocusedElement ?? document.body, {\n                                        select: true\n                                    });\n                                }\n                                container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                                focusScopesStack.remove(focusScope);\n                            }\n                        }[\"FocusScope.useEffect\"], 0);\n                    }\n                })[\"FocusScope.useEffect\"];\n            }\n        }\n    }[\"FocusScope.useEffect\"], [\n        container,\n        onMountAutoFocus,\n        onUnmountAutoFocus,\n        focusScope\n    ]);\n    const handleKeyDown = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"FocusScope.useCallback[handleKeyDown]\": (event)=>{\n            if (!loop && !trapped) return;\n            if (focusScope.paused) return;\n            const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n            const focusedElement = document.activeElement;\n            if (isTabKey && focusedElement) {\n                const container2 = event.currentTarget;\n                const [first, last] = getTabbableEdges(container2);\n                const hasTabbableElementsInside = first && last;\n                if (!hasTabbableElementsInside) {\n                    if (focusedElement === container2) event.preventDefault();\n                } else {\n                    if (!event.shiftKey && focusedElement === last) {\n                        event.preventDefault();\n                        if (loop) focus(first, {\n                            select: true\n                        });\n                    } else if (event.shiftKey && focusedElement === first) {\n                        event.preventDefault();\n                        if (loop) focus(last, {\n                            select: true\n                        });\n                    }\n                }\n            }\n        }\n    }[\"FocusScope.useCallback[handleKeyDown]\"], [\n        loop,\n        trapped,\n        focusScope.paused\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        tabIndex: -1,\n        ...scopeProps,\n        ref: composedRefs,\n        onKeyDown: handleKeyDown\n    });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n    const previouslyFocusedElement = document.activeElement;\n    for (const candidate of candidates){\n        focus(candidate, {\n            select\n        });\n        if (document.activeElement !== previouslyFocusedElement) return;\n    }\n}\nfunction getTabbableEdges(container) {\n    const candidates = getTabbableCandidates(container);\n    const first = findVisible(candidates, container);\n    const last = findVisible(candidates.reverse(), container);\n    return [\n        first,\n        last\n    ];\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction findVisible(elements, container) {\n    for (const element of elements){\n        if (!isHidden(element, {\n            upTo: container\n        })) return element;\n    }\n}\nfunction isHidden(node, { upTo }) {\n    if (getComputedStyle(node).visibility === \"hidden\") return true;\n    while(node){\n        if (upTo !== void 0 && node === upTo) return false;\n        if (getComputedStyle(node).display === \"none\") return true;\n        node = node.parentElement;\n    }\n    return false;\n}\nfunction isSelectableInput(element) {\n    return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n    if (element && element.focus) {\n        const previouslyFocusedElement = document.activeElement;\n        element.focus({\n            preventScroll: true\n        });\n        if (element !== previouslyFocusedElement && isSelectableInput(element) && select) element.select();\n    }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n    let stack = [];\n    return {\n        add (focusScope) {\n            const activeFocusScope = stack[0];\n            if (focusScope !== activeFocusScope) {\n                activeFocusScope?.pause();\n            }\n            stack = arrayRemove(stack, focusScope);\n            stack.unshift(focusScope);\n        },\n        remove (focusScope) {\n            stack = arrayRemove(stack, focusScope);\n            stack[0]?.resume();\n        }\n    };\n}\nfunction arrayRemove(array, item) {\n    const updatedArray = [\n        ...array\n    ];\n    const index = updatedArray.indexOf(item);\n    if (index !== -1) {\n        updatedArray.splice(index, 1);\n    }\n    return updatedArray;\n}\nfunction removeLinks(items) {\n    return items.filter((item)=>item.tagName !== \"A\");\n}\nvar Root = FocusScope;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@radix-ui/react-id/dist/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useId: () => (/* binding */ useId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/id/src/id.tsx\n\n\nvar useReactId = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\"useId\".toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(useReactId());\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWlkL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUMrQjtBQUNxQztBQUNwRSxpQkFBaUIseUxBQUs7QUFDdEI7QUFDQTtBQUNBLHNCQUFzQiwyQ0FBYztBQUNwQyxFQUFFLGtGQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILDJDQUEyQyxHQUFHO0FBQzlDO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsiRDpcXGJsb2ctZ2VuLWFpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHJlYWN0LWlkXFxkaXN0XFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvaWQvc3JjL2lkLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0XCI7XG52YXIgdXNlUmVhY3RJZCA9IFJlYWN0W1widXNlSWRcIi50b1N0cmluZygpXSB8fCAoKCkgPT4gdm9pZCAwKTtcbnZhciBjb3VudCA9IDA7XG5mdW5jdGlvbiB1c2VJZChkZXRlcm1pbmlzdGljSWQpIHtcbiAgY29uc3QgW2lkLCBzZXRJZF0gPSBSZWFjdC51c2VTdGF0ZSh1c2VSZWFjdElkKCkpO1xuICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghZGV0ZXJtaW5pc3RpY0lkKSBzZXRJZCgocmVhY3RJZCkgPT4gcmVhY3RJZCA/PyBTdHJpbmcoY291bnQrKykpO1xuICB9LCBbZGV0ZXJtaW5pc3RpY0lkXSk7XG4gIHJldHVybiBkZXRlcm1pbmlzdGljSWQgfHwgKGlkID8gYHJhZGl4LSR7aWR9YCA6IFwiXCIpO1xufVxuZXhwb3J0IHtcbiAgdXNlSWRcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-popper/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALIGN_OPTIONS: () => (/* binding */ ALIGN_OPTIONS),\n/* harmony export */   Anchor: () => (/* binding */ Anchor),\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Popper: () => (/* binding */ Popper),\n/* harmony export */   PopperAnchor: () => (/* binding */ PopperAnchor),\n/* harmony export */   PopperArrow: () => (/* binding */ PopperArrow),\n/* harmony export */   PopperContent: () => (/* binding */ PopperContent),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   SIDE_OPTIONS: () => (/* binding */ SIDE_OPTIONS),\n/* harmony export */   createPopperScope: () => (/* binding */ createPopperScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-arrow */ \"(ssr)/./node_modules/@radix-ui/react-arrow/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ALIGN_OPTIONS,Anchor,Arrow,Content,Popper,PopperAnchor,PopperArrow,PopperContent,Root,SIDE_OPTIONS,createPopperScope auto */ // packages/react/popper/src/Popper.tsx\n\n\n\n\n\n\n\n\n\n\nvar SIDE_OPTIONS = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nvar ALIGN_OPTIONS = [\n    \"start\",\n    \"center\",\n    \"end\"\n];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props)=>{\n    const { __scopePopper, children } = props;\n    const [anchor, setAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperProvider, {\n        scope: __scopePopper,\n        anchor,\n        onAnchorChange: setAnchor,\n        children\n    });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"PopperAnchor.useEffect\": ()=>{\n            context.onAnchorChange(virtualRef?.current || ref.current);\n        }\n    }[\"PopperAnchor.useEffect\"]);\n    return virtualRef ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...anchorProps,\n        ref: composedRefs\n    });\n});\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, side = \"bottom\", sideOffset = 0, align = \"center\", alignOffset = 0, arrowPadding = 0, avoidCollisions = true, collisionBoundary = [], collisionPadding: collisionPaddingProp = 0, sticky = \"partial\", hideWhenDetached = false, updatePositionStrategy = \"optimized\", onPlaced, ...contentProps } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"PopperContent.useComposedRefs[composedRefs]\": (node)=>setContent(node)\n    }[\"PopperContent.useComposedRefs[composedRefs]\"]);\n    const [arrow, setArrow] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const arrowSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__.useSize)(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...collisionPaddingProp\n    };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [\n        collisionBoundary\n    ];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n        padding: collisionPadding,\n        boundary: boundary.filter(isNotNull),\n        // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n        altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.useFloating)({\n        // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n        strategy: \"fixed\",\n        placement: desiredPlacement,\n        whileElementsMounted: {\n            \"PopperContent.useFloating\": (...args)=>{\n                const cleanup = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__.autoUpdate)(...args, {\n                    animationFrame: updatePositionStrategy === \"always\"\n                });\n                return cleanup;\n            }\n        }[\"PopperContent.useFloating\"],\n        elements: {\n            reference: context.anchor\n        },\n        middleware: [\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.offset)({\n                mainAxis: sideOffset + arrowHeight,\n                alignmentAxis: alignOffset\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.shift)({\n                mainAxis: true,\n                crossAxis: false,\n                limiter: sticky === \"partial\" ? (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.limitShift)() : void 0,\n                ...detectOverflowOptions\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.flip)({\n                ...detectOverflowOptions\n            }),\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.size)({\n                ...detectOverflowOptions,\n                apply: {\n                    \"PopperContent.useFloating\": ({ elements, rects, availableWidth, availableHeight })=>{\n                        const { width: anchorWidth, height: anchorHeight } = rects.reference;\n                        const contentStyle = elements.floating.style;\n                        contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n                        contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n                        contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n                        contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n                    }\n                }[\"PopperContent.useFloating\"]\n            }),\n            arrow && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.arrow)({\n                element: arrow,\n                padding: arrowPadding\n            }),\n            transformOrigin({\n                arrowWidth,\n                arrowHeight\n            }),\n            hideWhenDetached && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.hide)({\n                strategy: \"referenceHidden\",\n                ...detectOverflowOptions\n            })\n        ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onPlaced);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"PopperContent.useLayoutEffect\": ()=>{\n            if (isPositioned) {\n                handlePlaced?.();\n            }\n        }\n    }[\"PopperContent.useLayoutEffect\"], [\n        isPositioned,\n        handlePlaced\n    ]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"PopperContent.useLayoutEffect\": ()=>{\n            if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n        }\n    }[\"PopperContent.useLayoutEffect\"], [\n        content\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n            ...floatingStyles,\n            transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n            // keep off the page when measuring\n            minWidth: \"max-content\",\n            zIndex: contentZIndex,\n            [\"--radix-popper-transform-origin\"]: [\n                middlewareData.transformOrigin?.x,\n                middlewareData.transformOrigin?.y\n            ].join(\" \"),\n            // hide the content if using the hide middleware and should be hidden\n            // set visibility to hidden and disable pointer events so the UI behaves\n            // as if the PopperContent isn't there at all\n            ...middlewareData.hide?.referenceHidden && {\n                visibility: \"hidden\",\n                pointerEvents: \"none\"\n            }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperContentProvider, {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                    ...contentProps.style,\n                    // if the PopperContent hasn't been placed yet (not all measurements done)\n                    // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                    animation: !isPositioned ? \"none\" : void 0\n                }\n            })\n        })\n    });\n});\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n    top: \"bottom\",\n    right: \"left\",\n    bottom: \"top\",\n    left: \"right\"\n};\nvar PopperArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function PopperArrow2(props, forwardedRef) {\n    const { __scopePopper, ...arrowProps } = props;\n    const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n    const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n    return(// we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        ref: contentContext.onArrowChange,\n        style: {\n            position: \"absolute\",\n            left: contentContext.arrowX,\n            top: contentContext.arrowY,\n            [baseSide]: 0,\n            transformOrigin: {\n                top: \"\",\n                right: \"0 0\",\n                bottom: \"center 0\",\n                left: \"100% 0\"\n            }[contentContext.placedSide],\n            transform: {\n                top: \"translateY(100%)\",\n                right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n                bottom: `rotate(180deg)`,\n                left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n            }[contentContext.placedSide],\n            visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__.Root, {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n                ...arrowProps.style,\n                // ensures the element can be measured correctly (mostly for if SVG)\n                display: \"block\"\n            }\n        })\n    }));\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n    return value !== null;\n}\nvar transformOrigin = (options)=>({\n        name: \"transformOrigin\",\n        options,\n        fn (data) {\n            const { placement, rects, middlewareData } = data;\n            const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n            const isArrowHidden = cannotCenterArrow;\n            const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n            const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n            const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n            const noArrowAlign = {\n                start: \"0%\",\n                center: \"50%\",\n                end: \"100%\"\n            }[placedAlign];\n            const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n            const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n            let x = \"\";\n            let y = \"\";\n            if (placedSide === \"bottom\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${-arrowHeight}px`;\n            } else if (placedSide === \"top\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${rects.floating.height + arrowHeight}px`;\n            } else if (placedSide === \"right\") {\n                x = `${-arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            } else if (placedSide === \"left\") {\n                x = `${rects.floating.width + arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            }\n            return {\n                data: {\n                    x,\n                    y\n                }\n            };\n        }\n    });\nfunction getSideAndAlignFromPlacement(placement) {\n    const [side, align = \"center\"] = placement.split(\"-\");\n    return [\n        side,\n        align\n    ];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXBvcHBlci9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF1QjtBQVdoQjtBQUN5QjtBQUNBO0FBQ0c7QUFDVDtBQUNLO0FBQ0M7QUFDUjtBQWtDcEI7QUE1QkosSUFBTSxlQUFlO0lBQUM7SUFBTztJQUFTO0lBQVUsTUFBTTtDQUFBO0FBQ3RELElBQU0sZ0JBQWdCO0lBQUM7SUFBUztJQUFVLEtBQUs7Q0FBQTtBQVMvQyxJQUFNLGNBQWM7QUFHcEIsSUFBTSxDQUFDLHFCQUFxQixpQkFBaUIsSUFBSSwyRUFBa0IsQ0FBQyxXQUFXO0FBTS9FLElBQU0sQ0FBQyxnQkFBZ0IsZ0JBQWdCLElBQUksb0JBQXdDLFdBQVc7QUFLOUYsSUFBTSxTQUFnQyxDQUFDO0lBQ3JDLE1BQU0sRUFBRSxlQUFlLFNBQVMsSUFBSTtJQUNwQyxNQUFNLENBQUMsUUFBUSxTQUFTLElBQVUsNENBQTRCLElBQUk7SUFDbEUsT0FDRSx1RUFBQztRQUFlLE9BQU87UUFBZTtRQUFnQixnQkFBZ0I7UUFDbkU7SUFBQSxDQUNIO0FBRUo7QUFFQSxPQUFPLGNBQWM7QUFNckIsSUFBTSxjQUFjO0FBUXBCLElBQU0sNkJBQXFCLDhDQUN6QixDQUFDLE9BQXVDO0lBQ3RDLE1BQU0sRUFBRSxlQUFlLFlBQVksR0FBRyxZQUFZLElBQUk7SUFDdEQsTUFBTSxVQUFVLGlCQUFpQixhQUFhLGFBQWE7SUFDM0QsTUFBTSxNQUFZLDBDQUE0QixJQUFJO0lBQ2xELE1BQU0sZUFBZSw2RUFBZSxDQUFDLGNBQWMsR0FBRztJQUVoRDtrQ0FBVTtZQUlkLFFBQVEsZUFBZSxZQUFZLFdBQVcsSUFBSSxPQUFPO1FBQzNELENBQUM7O0lBRUQsT0FBTyxhQUFhLE9BQU8sdUVBQUMsZ0VBQVMsQ0FBQyxLQUFWO1FBQWUsR0FBRztRQUFhLEtBQUs7SUFBQSxDQUFjO0FBQ2hGO0FBR0YsYUFBYSxjQUFjO0FBTTNCLElBQU0sZUFBZTtBQVVyQixJQUFNLENBQUMsdUJBQXVCLGlCQUFpQixJQUM3QyxvQkFBK0MsWUFBWTtBQW9CN0QsSUFBTSw4QkFBc0IsOENBQzFCLENBQUMsT0FBd0M7SUFDdkMsTUFBTSxFQUNKLGVBQ0EsT0FBTyxVQUNQLGFBQWEsR0FDYixRQUFRLFVBQ1IsY0FBYyxHQUNkLGVBQWUsR0FDZixrQkFBa0IsTUFDbEIsb0JBQW9CLENBQUMsR0FDckIsa0JBQWtCLHVCQUF1QixHQUN6QyxTQUFTLFdBQ1QsbUJBQW1CLE9BQ25CLHlCQUF5QixhQUN6QixVQUNBLEdBQUcsY0FDTCxHQUFJO0lBRUosTUFBTSxVQUFVLGlCQUFpQixjQUFjLGFBQWE7SUFFNUQsTUFBTSxDQUFDLFNBQVMsVUFBVSxJQUFVLDRDQUFnQyxJQUFJO0lBQ3hFLE1BQU0sZUFBZSw2RUFBZSxDQUFDO3VEQUFjLENBQUMsT0FBUyxXQUFXLElBQUksQ0FBQzs7SUFFN0UsTUFBTSxDQUFDLE9BQU8sUUFBUSxJQUFVLDRDQUFpQyxJQUFJO0lBQ3JFLE1BQU0sWUFBWSxpRUFBTyxDQUFDLEtBQUs7SUFDL0IsTUFBTSxhQUFhLFdBQVcsU0FBUztJQUN2QyxNQUFNLGNBQWMsV0FBVyxVQUFVO0lBRXpDLE1BQU0sbUJBQW9CLFFBQVEsVUFBVSxXQUFXLE1BQU0sUUFBUTtJQUVyRSxNQUFNLG1CQUNKLE9BQU8seUJBQXlCLFdBQzVCLHVCQUNBO1FBQUUsS0FBSztRQUFHLE9BQU87UUFBRyxRQUFRO1FBQUcsTUFBTTtRQUFHLEdBQUc7SUFBcUI7SUFFdEUsTUFBTSxXQUFXLE1BQU0sUUFBUSxpQkFBaUIsSUFBSSxvQkFBb0I7UUFBQyxpQkFBaUI7S0FBQTtJQUMxRixNQUFNLHdCQUF3QixTQUFTLFNBQVM7SUFFaEQsTUFBTSx3QkFBd0I7UUFDNUIsU0FBUztRQUNULFVBQVUsU0FBUyxPQUFPLFNBQVM7UUFBQTtRQUVuQyxhQUFhO0lBQ2Y7SUFFQSxNQUFNLEVBQUUsTUFBTSxnQkFBZ0IsV0FBVyxjQUFjLGVBQWUsSUFBSSxtRUFBVyxDQUFDO1FBQUE7UUFFcEYsVUFBVTtRQUNWLFdBQVc7UUFDWDt5Q0FBc0IsSUFBSTtnQkFDeEIsTUFBTSxVQUFVLGtFQUFVLENBQUMsR0FBRyxNQUFNO29CQUNsQyxnQkFBZ0IsMkJBQTJCO2dCQUM3QyxDQUFDO2dCQUNELE9BQU87WUFDVDs7UUFDQSxVQUFVO1lBQ1IsV0FBVyxRQUFRO1FBQ3JCO1FBQ0EsWUFBWTtZQUNWLDhEQUFNLENBQUM7Z0JBQUUsVUFBVSxhQUFhO2dCQUFhLGVBQWU7WUFBWSxDQUFDO1lBQ3pFLG1CQUNFLDZEQUFLLENBQUM7Z0JBQ0osVUFBVTtnQkFDVixXQUFXO2dCQUNYLFNBQVMsV0FBVyxZQUFZLGtFQUFVLENBQUMsSUFBSTtnQkFDL0MsR0FBRztZQUNMLENBQUM7WUFDSCxtQkFBbUIsNERBQUksQ0FBQztnQkFBRSxHQUFHO1lBQXNCLENBQUM7WUFDcEQsNERBQUksQ0FBQztnQkFDSCxHQUFHO2dCQUNIO2lEQUFPLENBQUMsRUFBRSxVQUFVLE9BQU8sZ0JBQWdCLGdCQUFnQjt3QkFDekQsTUFBTSxFQUFFLE9BQU8sYUFBYSxRQUFRLGFBQWEsSUFBSSxNQUFNO3dCQUMzRCxNQUFNLGVBQWUsU0FBUyxTQUFTO3dCQUN2QyxhQUFhLFlBQVksa0NBQWtDLEdBQUcsY0FBYyxJQUFJO3dCQUNoRixhQUFhLFlBQVksbUNBQW1DLEdBQUcsZUFBZSxJQUFJO3dCQUNsRixhQUFhLFlBQVksK0JBQStCLEdBQUcsV0FBVyxJQUFJO3dCQUMxRSxhQUFhLFlBQVksZ0NBQWdDLEdBQUcsWUFBWSxJQUFJO29CQUM5RTs7WUFDRixDQUFDO1lBQ0QsU0FBUyw2REFBZSxDQUFDO2dCQUFFLFNBQVM7Z0JBQU8sU0FBUztZQUFhLENBQUM7WUFDbEUsZ0JBQWdCO2dCQUFFO2dCQUFZO1lBQVksQ0FBQztZQUMzQyxvQkFBb0IsNERBQUksQ0FBQztnQkFBRSxVQUFVO2dCQUFtQixHQUFHO1lBQXNCLENBQUM7U0FDcEY7SUFDRixDQUFDO0lBRUQsTUFBTSxDQUFDLFlBQVksV0FBVyxJQUFJLDZCQUE2QixTQUFTO0lBRXhFLE1BQU0sZUFBZSxnRkFBYyxDQUFDLFFBQVE7SUFDNUMsa0ZBQWU7eUNBQUM7WUFDZCxJQUFJLGNBQWM7Z0JBQ2hCLGVBQWU7WUFDakI7UUFDRjt3Q0FBRztRQUFDO1FBQWMsWUFBWTtLQUFDO0lBRS9CLE1BQU0sU0FBUyxlQUFlLE9BQU87SUFDckMsTUFBTSxTQUFTLGVBQWUsT0FBTztJQUNyQyxNQUFNLG9CQUFvQixlQUFlLE9BQU8saUJBQWlCO0lBRWpFLE1BQU0sQ0FBQyxlQUFlLGdCQUFnQixJQUFVLDRDQUFpQjtJQUNqRSxrRkFBZTt5Q0FBQztZQUNkLElBQUksUUFBUyxrQkFBaUIsT0FBTyxpQkFBaUIsT0FBTyxFQUFFLE1BQU07UUFDdkU7d0NBQUc7UUFBQyxPQUFPO0tBQUM7SUFFWixPQUNFLHVFQUFDO1FBQ0MsS0FBSyxLQUFLO1FBQ1YscUNBQWtDO1FBQ2xDLE9BQU87WUFDTCxHQUFHO1lBQ0gsV0FBVyxlQUFlLGVBQWUsWUFBWTtZQUFBO1lBQ3JELFVBQVU7WUFDVixRQUFRO1lBQ1IsQ0FBQyxpQ0FBd0MsR0FBRztnQkFDMUMsZUFBZSxpQkFBaUI7Z0JBQ2hDLGVBQWUsaUJBQWlCO2FBQ2xDLENBQUUsS0FBSyxHQUFHO1lBQUE7WUFBQTtZQUFBO1lBS1YsR0FBSSxlQUFlLE1BQU0sbUJBQW1CO2dCQUMxQyxZQUFZO2dCQUNaLGVBQWU7WUFDakI7UUFDRjtRQUlBLEtBQUssTUFBTTtRQUVYLGlGQUFDO1lBQ0MsT0FBTztZQUNQO1lBQ0EsZUFBZTtZQUNmO1lBQ0E7WUFDQSxpQkFBaUI7WUFFakIsaUZBQUMsZ0VBQVMsQ0FBQyxLQUFWO2dCQUNDLGFBQVc7Z0JBQ1gsY0FBWTtnQkFDWCxHQUFHO2dCQUNKLEtBQUs7Z0JBQ0wsT0FBTztvQkFDTCxHQUFHLGFBQWE7b0JBQUE7b0JBQUE7b0JBR2hCLFdBQVcsQ0FBQyxlQUFlLFNBQVM7Z0JBQ3RDO1lBQUE7UUFDRjtJQUNGO0FBR047QUFHRixjQUFjLGNBQWM7QUFNNUIsSUFBTSxhQUFhO0FBRW5CLElBQU0sZ0JBQW9DO0lBQ3hDLEtBQUs7SUFDTCxPQUFPO0lBQ1AsUUFBUTtJQUNSLE1BQU07QUFDUjtBQU1BLElBQU0sNEJBQW9CLDhDQUFpRCxTQUFTQSxhQUNsRixPQUNBLGNBQ0E7SUFDQSxNQUFNLEVBQUUsZUFBZSxHQUFHLFdBQVcsSUFBSTtJQUN6QyxNQUFNLGlCQUFpQixrQkFBa0IsWUFBWSxhQUFhO0lBQ2xFLE1BQU0sV0FBVyxjQUFjLGVBQWUsVUFBVTtJQUV4RDtJQUFBO0lBQUE7SUFJRSx1RUFBQztRQUNDLEtBQUssZUFBZTtRQUNwQixPQUFPO1lBQ0wsVUFBVTtZQUNWLE1BQU0sZUFBZTtZQUNyQixLQUFLLGVBQWU7WUFDcEIsQ0FBQyxRQUFRLEdBQUc7WUFDWixpQkFBaUI7Z0JBQ2YsS0FBSztnQkFDTCxPQUFPO2dCQUNQLFFBQVE7Z0JBQ1IsTUFBTTtZQUNSLEVBQUUsZUFBZSxVQUFVO1lBQzNCLFdBQVc7Z0JBQ1QsS0FBSztnQkFDTCxPQUFPO2dCQUNQLFFBQVE7Z0JBQ1IsTUFBTTtZQUNSLEVBQUUsZUFBZSxVQUFVO1lBQzNCLFlBQVksZUFBZSxrQkFBa0IsV0FBVztRQUMxRDtRQUVBLGlGQUFnQiwwREFBZjtZQUNFLEdBQUc7WUFDSixLQUFLO1lBQ0wsT0FBTztnQkFDTCxHQUFHLFdBQVc7Z0JBQUE7Z0JBRWQsU0FBUztZQUNYO1FBQUE7SUFDRjtBQUdOLENBQUM7QUFFRCxZQUFZLGNBQWM7QUFJMUIsU0FBUyxVQUFhLE9BQTZCO0lBQ2pELE9BQU8sVUFBVTtBQUNuQjtBQUVBLElBQU0sa0JBQWtCLENBQUMsV0FBc0U7UUFDN0YsTUFBTTtRQUNOO1FBQ0EsSUFBRyxNQUFNO1lBQ1AsTUFBTSxFQUFFLFdBQVcsT0FBTyxlQUFlLElBQUk7WUFFN0MsTUFBTSxvQkFBb0IsZUFBZSxPQUFPLGlCQUFpQjtZQUNqRSxNQUFNLGdCQUFnQjtZQUN0QixNQUFNLGFBQWEsZ0JBQWdCLElBQUksUUFBUTtZQUMvQyxNQUFNLGNBQWMsZ0JBQWdCLElBQUksUUFBUTtZQUVoRCxNQUFNLENBQUMsWUFBWSxXQUFXLElBQUksNkJBQTZCLFNBQVM7WUFDeEUsTUFBTSxlQUFlO2dCQUFFLE9BQU87Z0JBQU0sUUFBUTtnQkFBTyxLQUFLO1lBQU8sRUFBRSxXQUFXO1lBRTVFLE1BQU0sZ0JBQWdCLGVBQWUsT0FBTyxNQUFLLElBQUssYUFBYTtZQUNuRSxNQUFNLGdCQUFnQixlQUFlLE9BQU8sTUFBSyxJQUFLLGNBQWM7WUFFcEUsSUFBSSxJQUFJO1lBQ1IsSUFBSSxJQUFJO1lBRVIsSUFBSSxlQUFlLFVBQVU7Z0JBQzNCLElBQUksZ0JBQWdCLGVBQWUsR0FBRyxZQUFZO2dCQUNsRCxJQUFJLEdBQUcsQ0FBQyxXQUFXO1lBQ3JCLFdBQVcsZUFBZSxPQUFPO2dCQUMvQixJQUFJLGdCQUFnQixlQUFlLEdBQUcsWUFBWTtnQkFDbEQsSUFBSSxHQUFHLE1BQU0sU0FBUyxTQUFTLFdBQVc7WUFDNUMsV0FBVyxlQUFlLFNBQVM7Z0JBQ2pDLElBQUksR0FBRyxDQUFDLFdBQVc7Z0JBQ25CLElBQUksZ0JBQWdCLGVBQWUsR0FBRyxZQUFZO1lBQ3BELFdBQVcsZUFBZSxRQUFRO2dCQUNoQyxJQUFJLEdBQUcsTUFBTSxTQUFTLFFBQVEsV0FBVztnQkFDekMsSUFBSSxnQkFBZ0IsZUFBZSxHQUFHLFlBQVk7WUFDcEQ7WUFDQSxPQUFPO2dCQUFFLE1BQU07b0JBQUU7b0JBQUc7Z0JBQUU7WUFBRTtRQUMxQjtLQUNGO0FBRUEsU0FBUyw2QkFBNkIsV0FBc0I7SUFDMUQsTUFBTSxDQUFDLE1BQU0sUUFBUSxRQUFRLElBQUksVUFBVSxNQUFNLEdBQUc7SUFDcEQsT0FBTztRQUFDO1FBQWMsS0FBYztLQUFBO0FBQ3RDO0FBRUEsSUFBTUMsUUFBTztBQUNiLElBQU0sU0FBUztBQUNmLElBQU0sVUFBVTtBQUNoQixJQUFNLFFBQVEiLCJzb3VyY2VzIjpbIkQ6XFxibG9nLWdlbi1haVxcc3JjXFxQb3BwZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7XG4gIHVzZUZsb2F0aW5nLFxuICBhdXRvVXBkYXRlLFxuICBvZmZzZXQsXG4gIHNoaWZ0LFxuICBsaW1pdFNoaWZ0LFxuICBoaWRlLFxuICBhcnJvdyBhcyBmbG9hdGluZ1VJYXJyb3csXG4gIGZsaXAsXG4gIHNpemUsXG59IGZyb20gJ0BmbG9hdGluZy11aS9yZWFjdC1kb20nO1xuaW1wb3J0ICogYXMgQXJyb3dQcmltaXRpdmUgZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWFycm93JztcbmltcG9ydCB7IHVzZUNvbXBvc2VkUmVmcyB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1jb21wb3NlLXJlZnMnO1xuaW1wb3J0IHsgY3JlYXRlQ29udGV4dFNjb3BlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWNvbnRleHQnO1xuaW1wb3J0IHsgUHJpbWl0aXZlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZSc7XG5pbXBvcnQgeyB1c2VDYWxsYmFja1JlZiB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmJztcbmltcG9ydCB7IHVzZUxheW91dEVmZmVjdCB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdCc7XG5pbXBvcnQgeyB1c2VTaXplIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXVzZS1zaXplJztcblxuaW1wb3J0IHR5cGUgeyBQbGFjZW1lbnQsIE1pZGRsZXdhcmUgfSBmcm9tICdAZmxvYXRpbmctdWkvcmVhY3QtZG9tJztcbmltcG9ydCB0eXBlIHsgU2NvcGUgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtY29udGV4dCc7XG5pbXBvcnQgdHlwZSB7IE1lYXN1cmFibGUgfSBmcm9tICdAcmFkaXgtdWkvcmVjdCc7XG5cbmNvbnN0IFNJREVfT1BUSU9OUyA9IFsndG9wJywgJ3JpZ2h0JywgJ2JvdHRvbScsICdsZWZ0J10gYXMgY29uc3Q7XG5jb25zdCBBTElHTl9PUFRJT05TID0gWydzdGFydCcsICdjZW50ZXInLCAnZW5kJ10gYXMgY29uc3Q7XG5cbnR5cGUgU2lkZSA9ICh0eXBlb2YgU0lERV9PUFRJT05TKVtudW1iZXJdO1xudHlwZSBBbGlnbiA9ICh0eXBlb2YgQUxJR05fT1BUSU9OUylbbnVtYmVyXTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogUG9wcGVyXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IFBPUFBFUl9OQU1FID0gJ1BvcHBlcic7XG5cbnR5cGUgU2NvcGVkUHJvcHM8UD4gPSBQICYgeyBfX3Njb3BlUG9wcGVyPzogU2NvcGUgfTtcbmNvbnN0IFtjcmVhdGVQb3BwZXJDb250ZXh0LCBjcmVhdGVQb3BwZXJTY29wZV0gPSBjcmVhdGVDb250ZXh0U2NvcGUoUE9QUEVSX05BTUUpO1xuXG50eXBlIFBvcHBlckNvbnRleHRWYWx1ZSA9IHtcbiAgYW5jaG9yOiBNZWFzdXJhYmxlIHwgbnVsbDtcbiAgb25BbmNob3JDaGFuZ2UoYW5jaG9yOiBNZWFzdXJhYmxlIHwgbnVsbCk6IHZvaWQ7XG59O1xuY29uc3QgW1BvcHBlclByb3ZpZGVyLCB1c2VQb3BwZXJDb250ZXh0XSA9IGNyZWF0ZVBvcHBlckNvbnRleHQ8UG9wcGVyQ29udGV4dFZhbHVlPihQT1BQRVJfTkFNRSk7XG5cbmludGVyZmFjZSBQb3BwZXJQcm9wcyB7XG4gIGNoaWxkcmVuPzogUmVhY3QuUmVhY3ROb2RlO1xufVxuY29uc3QgUG9wcGVyOiBSZWFjdC5GQzxQb3BwZXJQcm9wcz4gPSAocHJvcHM6IFNjb3BlZFByb3BzPFBvcHBlclByb3BzPikgPT4ge1xuICBjb25zdCB7IF9fc2NvcGVQb3BwZXIsIGNoaWxkcmVuIH0gPSBwcm9wcztcbiAgY29uc3QgW2FuY2hvciwgc2V0QW5jaG9yXSA9IFJlYWN0LnVzZVN0YXRlPE1lYXN1cmFibGUgfCBudWxsPihudWxsKTtcbiAgcmV0dXJuIChcbiAgICA8UG9wcGVyUHJvdmlkZXIgc2NvcGU9e19fc2NvcGVQb3BwZXJ9IGFuY2hvcj17YW5jaG9yfSBvbkFuY2hvckNoYW5nZT17c2V0QW5jaG9yfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1BvcHBlclByb3ZpZGVyPlxuICApO1xufTtcblxuUG9wcGVyLmRpc3BsYXlOYW1lID0gUE9QUEVSX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFBvcHBlckFuY2hvclxuICogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5jb25zdCBBTkNIT1JfTkFNRSA9ICdQb3BwZXJBbmNob3InO1xuXG50eXBlIFBvcHBlckFuY2hvckVsZW1lbnQgPSBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBQcmltaXRpdmUuZGl2PjtcbnR5cGUgUHJpbWl0aXZlRGl2UHJvcHMgPSBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xuaW50ZXJmYWNlIFBvcHBlckFuY2hvclByb3BzIGV4dGVuZHMgUHJpbWl0aXZlRGl2UHJvcHMge1xuICB2aXJ0dWFsUmVmPzogUmVhY3QuUmVmT2JqZWN0PE1lYXN1cmFibGU+O1xufVxuXG5jb25zdCBQb3BwZXJBbmNob3IgPSBSZWFjdC5mb3J3YXJkUmVmPFBvcHBlckFuY2hvckVsZW1lbnQsIFBvcHBlckFuY2hvclByb3BzPihcbiAgKHByb3BzOiBTY29wZWRQcm9wczxQb3BwZXJBbmNob3JQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHsgX19zY29wZVBvcHBlciwgdmlydHVhbFJlZiwgLi4uYW5jaG9yUHJvcHMgfSA9IHByb3BzO1xuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VQb3BwZXJDb250ZXh0KEFOQ0hPUl9OQU1FLCBfX3Njb3BlUG9wcGVyKTtcbiAgICBjb25zdCByZWYgPSBSZWFjdC51c2VSZWY8UG9wcGVyQW5jaG9yRWxlbWVudD4obnVsbCk7XG4gICAgY29uc3QgY29tcG9zZWRSZWZzID0gdXNlQ29tcG9zZWRSZWZzKGZvcndhcmRlZFJlZiwgcmVmKTtcblxuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAvLyBDb25zdW1lciBjYW4gYW5jaG9yIHRoZSBwb3BwZXIgdG8gc29tZXRoaW5nIHRoYXQgaXNuJ3RcbiAgICAgIC8vIGEgRE9NIG5vZGUgZS5nLiBwb2ludGVyIHBvc2l0aW9uLCBzbyB3ZSBvdmVycmlkZSB0aGVcbiAgICAgIC8vIGBhbmNob3JSZWZgIHdpdGggdGhlaXIgdmlydHVhbCByZWYgaW4gdGhpcyBjYXNlLlxuICAgICAgY29udGV4dC5vbkFuY2hvckNoYW5nZSh2aXJ0dWFsUmVmPy5jdXJyZW50IHx8IHJlZi5jdXJyZW50KTtcbiAgICB9KTtcblxuICAgIHJldHVybiB2aXJ0dWFsUmVmID8gbnVsbCA6IDxQcmltaXRpdmUuZGl2IHsuLi5hbmNob3JQcm9wc30gcmVmPXtjb21wb3NlZFJlZnN9IC8+O1xuICB9XG4pO1xuXG5Qb3BwZXJBbmNob3IuZGlzcGxheU5hbWUgPSBBTkNIT1JfTkFNRTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogUG9wcGVyQ29udGVudFxuICogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5jb25zdCBDT05URU5UX05BTUUgPSAnUG9wcGVyQ29udGVudCc7XG5cbnR5cGUgUG9wcGVyQ29udGVudENvbnRleHRWYWx1ZSA9IHtcbiAgcGxhY2VkU2lkZTogU2lkZTtcbiAgb25BcnJvd0NoYW5nZShhcnJvdzogSFRNTFNwYW5FbGVtZW50IHwgbnVsbCk6IHZvaWQ7XG4gIGFycm93WD86IG51bWJlcjtcbiAgYXJyb3dZPzogbnVtYmVyO1xuICBzaG91bGRIaWRlQXJyb3c6IGJvb2xlYW47XG59O1xuXG5jb25zdCBbUG9wcGVyQ29udGVudFByb3ZpZGVyLCB1c2VDb250ZW50Q29udGV4dF0gPVxuICBjcmVhdGVQb3BwZXJDb250ZXh0PFBvcHBlckNvbnRlbnRDb250ZXh0VmFsdWU+KENPTlRFTlRfTkFNRSk7XG5cbnR5cGUgQm91bmRhcnkgPSBFbGVtZW50IHwgbnVsbDtcblxudHlwZSBQb3BwZXJDb250ZW50RWxlbWVudCA9IFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xuaW50ZXJmYWNlIFBvcHBlckNvbnRlbnRQcm9wcyBleHRlbmRzIFByaW1pdGl2ZURpdlByb3BzIHtcbiAgc2lkZT86IFNpZGU7XG4gIHNpZGVPZmZzZXQ/OiBudW1iZXI7XG4gIGFsaWduPzogQWxpZ247XG4gIGFsaWduT2Zmc2V0PzogbnVtYmVyO1xuICBhcnJvd1BhZGRpbmc/OiBudW1iZXI7XG4gIGF2b2lkQ29sbGlzaW9ucz86IGJvb2xlYW47XG4gIGNvbGxpc2lvbkJvdW5kYXJ5PzogQm91bmRhcnkgfCBCb3VuZGFyeVtdO1xuICBjb2xsaXNpb25QYWRkaW5nPzogbnVtYmVyIHwgUGFydGlhbDxSZWNvcmQ8U2lkZSwgbnVtYmVyPj47XG4gIHN0aWNreT86ICdwYXJ0aWFsJyB8ICdhbHdheXMnO1xuICBoaWRlV2hlbkRldGFjaGVkPzogYm9vbGVhbjtcbiAgdXBkYXRlUG9zaXRpb25TdHJhdGVneT86ICdvcHRpbWl6ZWQnIHwgJ2Fsd2F5cyc7XG4gIG9uUGxhY2VkPzogKCkgPT4gdm9pZDtcbn1cblxuY29uc3QgUG9wcGVyQ29udGVudCA9IFJlYWN0LmZvcndhcmRSZWY8UG9wcGVyQ29udGVudEVsZW1lbnQsIFBvcHBlckNvbnRlbnRQcm9wcz4oXG4gIChwcm9wczogU2NvcGVkUHJvcHM8UG9wcGVyQ29udGVudFByb3BzPiwgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3Qge1xuICAgICAgX19zY29wZVBvcHBlcixcbiAgICAgIHNpZGUgPSAnYm90dG9tJyxcbiAgICAgIHNpZGVPZmZzZXQgPSAwLFxuICAgICAgYWxpZ24gPSAnY2VudGVyJyxcbiAgICAgIGFsaWduT2Zmc2V0ID0gMCxcbiAgICAgIGFycm93UGFkZGluZyA9IDAsXG4gICAgICBhdm9pZENvbGxpc2lvbnMgPSB0cnVlLFxuICAgICAgY29sbGlzaW9uQm91bmRhcnkgPSBbXSxcbiAgICAgIGNvbGxpc2lvblBhZGRpbmc6IGNvbGxpc2lvblBhZGRpbmdQcm9wID0gMCxcbiAgICAgIHN0aWNreSA9ICdwYXJ0aWFsJyxcbiAgICAgIGhpZGVXaGVuRGV0YWNoZWQgPSBmYWxzZSxcbiAgICAgIHVwZGF0ZVBvc2l0aW9uU3RyYXRlZ3kgPSAnb3B0aW1pemVkJyxcbiAgICAgIG9uUGxhY2VkLFxuICAgICAgLi4uY29udGVudFByb3BzXG4gICAgfSA9IHByb3BzO1xuXG4gICAgY29uc3QgY29udGV4dCA9IHVzZVBvcHBlckNvbnRleHQoQ09OVEVOVF9OQU1FLCBfX3Njb3BlUG9wcGVyKTtcblxuICAgIGNvbnN0IFtjb250ZW50LCBzZXRDb250ZW50XSA9IFJlYWN0LnVzZVN0YXRlPEhUTUxEaXZFbGVtZW50IHwgbnVsbD4obnVsbCk7XG4gICAgY29uc3QgY29tcG9zZWRSZWZzID0gdXNlQ29tcG9zZWRSZWZzKGZvcndhcmRlZFJlZiwgKG5vZGUpID0+IHNldENvbnRlbnQobm9kZSkpO1xuXG4gICAgY29uc3QgW2Fycm93LCBzZXRBcnJvd10gPSBSZWFjdC51c2VTdGF0ZTxIVE1MU3BhbkVsZW1lbnQgfCBudWxsPihudWxsKTtcbiAgICBjb25zdCBhcnJvd1NpemUgPSB1c2VTaXplKGFycm93KTtcbiAgICBjb25zdCBhcnJvd1dpZHRoID0gYXJyb3dTaXplPy53aWR0aCA/PyAwO1xuICAgIGNvbnN0IGFycm93SGVpZ2h0ID0gYXJyb3dTaXplPy5oZWlnaHQgPz8gMDtcblxuICAgIGNvbnN0IGRlc2lyZWRQbGFjZW1lbnQgPSAoc2lkZSArIChhbGlnbiAhPT0gJ2NlbnRlcicgPyAnLScgKyBhbGlnbiA6ICcnKSkgYXMgUGxhY2VtZW50O1xuXG4gICAgY29uc3QgY29sbGlzaW9uUGFkZGluZyA9XG4gICAgICB0eXBlb2YgY29sbGlzaW9uUGFkZGluZ1Byb3AgPT09ICdudW1iZXInXG4gICAgICAgID8gY29sbGlzaW9uUGFkZGluZ1Byb3BcbiAgICAgICAgOiB7IHRvcDogMCwgcmlnaHQ6IDAsIGJvdHRvbTogMCwgbGVmdDogMCwgLi4uY29sbGlzaW9uUGFkZGluZ1Byb3AgfTtcblxuICAgIGNvbnN0IGJvdW5kYXJ5ID0gQXJyYXkuaXNBcnJheShjb2xsaXNpb25Cb3VuZGFyeSkgPyBjb2xsaXNpb25Cb3VuZGFyeSA6IFtjb2xsaXNpb25Cb3VuZGFyeV07XG4gICAgY29uc3QgaGFzRXhwbGljaXRCb3VuZGFyaWVzID0gYm91bmRhcnkubGVuZ3RoID4gMDtcblxuICAgIGNvbnN0IGRldGVjdE92ZXJmbG93T3B0aW9ucyA9IHtcbiAgICAgIHBhZGRpbmc6IGNvbGxpc2lvblBhZGRpbmcsXG4gICAgICBib3VuZGFyeTogYm91bmRhcnkuZmlsdGVyKGlzTm90TnVsbCksXG4gICAgICAvLyB3aXRoIGBzdHJhdGVneTogJ2ZpeGVkJ2AsIHRoaXMgaXMgdGhlIG9ubHkgd2F5IHRvIGdldCBpdCB0byByZXNwZWN0IGJvdW5kYXJpZXNcbiAgICAgIGFsdEJvdW5kYXJ5OiBoYXNFeHBsaWNpdEJvdW5kYXJpZXMsXG4gICAgfTtcblxuICAgIGNvbnN0IHsgcmVmcywgZmxvYXRpbmdTdHlsZXMsIHBsYWNlbWVudCwgaXNQb3NpdGlvbmVkLCBtaWRkbGV3YXJlRGF0YSB9ID0gdXNlRmxvYXRpbmcoe1xuICAgICAgLy8gZGVmYXVsdCB0byBgZml4ZWRgIHN0cmF0ZWd5IHNvIHVzZXJzIGRvbid0IGhhdmUgdG8gcGljayBhbmQgd2UgYWxzbyBhdm9pZCBmb2N1cyBzY3JvbGwgaXNzdWVzXG4gICAgICBzdHJhdGVneTogJ2ZpeGVkJyxcbiAgICAgIHBsYWNlbWVudDogZGVzaXJlZFBsYWNlbWVudCxcbiAgICAgIHdoaWxlRWxlbWVudHNNb3VudGVkOiAoLi4uYXJncykgPT4ge1xuICAgICAgICBjb25zdCBjbGVhbnVwID0gYXV0b1VwZGF0ZSguLi5hcmdzLCB7XG4gICAgICAgICAgYW5pbWF0aW9uRnJhbWU6IHVwZGF0ZVBvc2l0aW9uU3RyYXRlZ3kgPT09ICdhbHdheXMnLFxuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIGNsZWFudXA7XG4gICAgICB9LFxuICAgICAgZWxlbWVudHM6IHtcbiAgICAgICAgcmVmZXJlbmNlOiBjb250ZXh0LmFuY2hvcixcbiAgICAgIH0sXG4gICAgICBtaWRkbGV3YXJlOiBbXG4gICAgICAgIG9mZnNldCh7IG1haW5BeGlzOiBzaWRlT2Zmc2V0ICsgYXJyb3dIZWlnaHQsIGFsaWdubWVudEF4aXM6IGFsaWduT2Zmc2V0IH0pLFxuICAgICAgICBhdm9pZENvbGxpc2lvbnMgJiZcbiAgICAgICAgICBzaGlmdCh7XG4gICAgICAgICAgICBtYWluQXhpczogdHJ1ZSxcbiAgICAgICAgICAgIGNyb3NzQXhpczogZmFsc2UsXG4gICAgICAgICAgICBsaW1pdGVyOiBzdGlja3kgPT09ICdwYXJ0aWFsJyA/IGxpbWl0U2hpZnQoKSA6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIC4uLmRldGVjdE92ZXJmbG93T3B0aW9ucyxcbiAgICAgICAgICB9KSxcbiAgICAgICAgYXZvaWRDb2xsaXNpb25zICYmIGZsaXAoeyAuLi5kZXRlY3RPdmVyZmxvd09wdGlvbnMgfSksXG4gICAgICAgIHNpemUoe1xuICAgICAgICAgIC4uLmRldGVjdE92ZXJmbG93T3B0aW9ucyxcbiAgICAgICAgICBhcHBseTogKHsgZWxlbWVudHMsIHJlY3RzLCBhdmFpbGFibGVXaWR0aCwgYXZhaWxhYmxlSGVpZ2h0IH0pID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHsgd2lkdGg6IGFuY2hvcldpZHRoLCBoZWlnaHQ6IGFuY2hvckhlaWdodCB9ID0gcmVjdHMucmVmZXJlbmNlO1xuICAgICAgICAgICAgY29uc3QgY29udGVudFN0eWxlID0gZWxlbWVudHMuZmxvYXRpbmcuc3R5bGU7XG4gICAgICAgICAgICBjb250ZW50U3R5bGUuc2V0UHJvcGVydHkoJy0tcmFkaXgtcG9wcGVyLWF2YWlsYWJsZS13aWR0aCcsIGAke2F2YWlsYWJsZVdpZHRofXB4YCk7XG4gICAgICAgICAgICBjb250ZW50U3R5bGUuc2V0UHJvcGVydHkoJy0tcmFkaXgtcG9wcGVyLWF2YWlsYWJsZS1oZWlnaHQnLCBgJHthdmFpbGFibGVIZWlnaHR9cHhgKTtcbiAgICAgICAgICAgIGNvbnRlbnRTdHlsZS5zZXRQcm9wZXJ0eSgnLS1yYWRpeC1wb3BwZXItYW5jaG9yLXdpZHRoJywgYCR7YW5jaG9yV2lkdGh9cHhgKTtcbiAgICAgICAgICAgIGNvbnRlbnRTdHlsZS5zZXRQcm9wZXJ0eSgnLS1yYWRpeC1wb3BwZXItYW5jaG9yLWhlaWdodCcsIGAke2FuY2hvckhlaWdodH1weGApO1xuICAgICAgICAgIH0sXG4gICAgICAgIH0pLFxuICAgICAgICBhcnJvdyAmJiBmbG9hdGluZ1VJYXJyb3coeyBlbGVtZW50OiBhcnJvdywgcGFkZGluZzogYXJyb3dQYWRkaW5nIH0pLFxuICAgICAgICB0cmFuc2Zvcm1PcmlnaW4oeyBhcnJvd1dpZHRoLCBhcnJvd0hlaWdodCB9KSxcbiAgICAgICAgaGlkZVdoZW5EZXRhY2hlZCAmJiBoaWRlKHsgc3RyYXRlZ3k6ICdyZWZlcmVuY2VIaWRkZW4nLCAuLi5kZXRlY3RPdmVyZmxvd09wdGlvbnMgfSksXG4gICAgICBdLFxuICAgIH0pO1xuXG4gICAgY29uc3QgW3BsYWNlZFNpZGUsIHBsYWNlZEFsaWduXSA9IGdldFNpZGVBbmRBbGlnbkZyb21QbGFjZW1lbnQocGxhY2VtZW50KTtcblxuICAgIGNvbnN0IGhhbmRsZVBsYWNlZCA9IHVzZUNhbGxiYWNrUmVmKG9uUGxhY2VkKTtcbiAgICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgICAgaWYgKGlzUG9zaXRpb25lZCkge1xuICAgICAgICBoYW5kbGVQbGFjZWQ/LigpO1xuICAgICAgfVxuICAgIH0sIFtpc1Bvc2l0aW9uZWQsIGhhbmRsZVBsYWNlZF0pO1xuXG4gICAgY29uc3QgYXJyb3dYID0gbWlkZGxld2FyZURhdGEuYXJyb3c/Lng7XG4gICAgY29uc3QgYXJyb3dZID0gbWlkZGxld2FyZURhdGEuYXJyb3c/Lnk7XG4gICAgY29uc3QgY2Fubm90Q2VudGVyQXJyb3cgPSBtaWRkbGV3YXJlRGF0YS5hcnJvdz8uY2VudGVyT2Zmc2V0ICE9PSAwO1xuXG4gICAgY29uc3QgW2NvbnRlbnRaSW5kZXgsIHNldENvbnRlbnRaSW5kZXhdID0gUmVhY3QudXNlU3RhdGU8c3RyaW5nPigpO1xuICAgIHVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgICBpZiAoY29udGVudCkgc2V0Q29udGVudFpJbmRleCh3aW5kb3cuZ2V0Q29tcHV0ZWRTdHlsZShjb250ZW50KS56SW5kZXgpO1xuICAgIH0sIFtjb250ZW50XSk7XG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdlxuICAgICAgICByZWY9e3JlZnMuc2V0RmxvYXRpbmd9XG4gICAgICAgIGRhdGEtcmFkaXgtcG9wcGVyLWNvbnRlbnQtd3JhcHBlcj1cIlwiXG4gICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgLi4uZmxvYXRpbmdTdHlsZXMsXG4gICAgICAgICAgdHJhbnNmb3JtOiBpc1Bvc2l0aW9uZWQgPyBmbG9hdGluZ1N0eWxlcy50cmFuc2Zvcm0gOiAndHJhbnNsYXRlKDAsIC0yMDAlKScsIC8vIGtlZXAgb2ZmIHRoZSBwYWdlIHdoZW4gbWVhc3VyaW5nXG4gICAgICAgICAgbWluV2lkdGg6ICdtYXgtY29udGVudCcsXG4gICAgICAgICAgekluZGV4OiBjb250ZW50WkluZGV4LFxuICAgICAgICAgIFsnLS1yYWRpeC1wb3BwZXItdHJhbnNmb3JtLW9yaWdpbicgYXMgYW55XTogW1xuICAgICAgICAgICAgbWlkZGxld2FyZURhdGEudHJhbnNmb3JtT3JpZ2luPy54LFxuICAgICAgICAgICAgbWlkZGxld2FyZURhdGEudHJhbnNmb3JtT3JpZ2luPy55LFxuICAgICAgICAgIF0uam9pbignICcpLFxuXG4gICAgICAgICAgLy8gaGlkZSB0aGUgY29udGVudCBpZiB1c2luZyB0aGUgaGlkZSBtaWRkbGV3YXJlIGFuZCBzaG91bGQgYmUgaGlkZGVuXG4gICAgICAgICAgLy8gc2V0IHZpc2liaWxpdHkgdG8gaGlkZGVuIGFuZCBkaXNhYmxlIHBvaW50ZXIgZXZlbnRzIHNvIHRoZSBVSSBiZWhhdmVzXG4gICAgICAgICAgLy8gYXMgaWYgdGhlIFBvcHBlckNvbnRlbnQgaXNuJ3QgdGhlcmUgYXQgYWxsXG4gICAgICAgICAgLi4uKG1pZGRsZXdhcmVEYXRhLmhpZGU/LnJlZmVyZW5jZUhpZGRlbiAmJiB7XG4gICAgICAgICAgICB2aXNpYmlsaXR5OiAnaGlkZGVuJyxcbiAgICAgICAgICAgIHBvaW50ZXJFdmVudHM6ICdub25lJyxcbiAgICAgICAgICB9KSxcbiAgICAgICAgfX1cbiAgICAgICAgLy8gRmxvYXRpbmcgVUkgaW50ZXJhbGx5IGNhbGN1bGF0ZXMgbG9naWNhbCBhbGlnbm1lbnQgYmFzZWQgdGhlIGBkaXJgIGF0dHJpYnV0ZSBvblxuICAgICAgICAvLyB0aGUgcmVmZXJlbmNlL2Zsb2F0aW5nIG5vZGUsIHdlIG11c3QgYWRkIHRoaXMgYXR0cmlidXRlIGhlcmUgdG8gZW5zdXJlXG4gICAgICAgIC8vIHRoaXMgaXMgY2FsY3VsYXRlZCB3aGVuIHBvcnRhbGxlZCBhcyB3ZWxsIGFzIGlubGluZS5cbiAgICAgICAgZGlyPXtwcm9wcy5kaXJ9XG4gICAgICA+XG4gICAgICAgIDxQb3BwZXJDb250ZW50UHJvdmlkZXJcbiAgICAgICAgICBzY29wZT17X19zY29wZVBvcHBlcn1cbiAgICAgICAgICBwbGFjZWRTaWRlPXtwbGFjZWRTaWRlfVxuICAgICAgICAgIG9uQXJyb3dDaGFuZ2U9e3NldEFycm93fVxuICAgICAgICAgIGFycm93WD17YXJyb3dYfVxuICAgICAgICAgIGFycm93WT17YXJyb3dZfVxuICAgICAgICAgIHNob3VsZEhpZGVBcnJvdz17Y2Fubm90Q2VudGVyQXJyb3d9XG4gICAgICAgID5cbiAgICAgICAgICA8UHJpbWl0aXZlLmRpdlxuICAgICAgICAgICAgZGF0YS1zaWRlPXtwbGFjZWRTaWRlfVxuICAgICAgICAgICAgZGF0YS1hbGlnbj17cGxhY2VkQWxpZ259XG4gICAgICAgICAgICB7Li4uY29udGVudFByb3BzfVxuICAgICAgICAgICAgcmVmPXtjb21wb3NlZFJlZnN9XG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAuLi5jb250ZW50UHJvcHMuc3R5bGUsXG4gICAgICAgICAgICAgIC8vIGlmIHRoZSBQb3BwZXJDb250ZW50IGhhc24ndCBiZWVuIHBsYWNlZCB5ZXQgKG5vdCBhbGwgbWVhc3VyZW1lbnRzIGRvbmUpXG4gICAgICAgICAgICAgIC8vIHdlIHByZXZlbnQgYW5pbWF0aW9ucyBzbyB0aGF0IHVzZXJzJ3MgYW5pbWF0aW9uIGRvbid0IGtpY2sgaW4gdG9vIGVhcmx5IHJlZmVycmluZyB3cm9uZyBzaWRlc1xuICAgICAgICAgICAgICBhbmltYXRpb246ICFpc1Bvc2l0aW9uZWQgPyAnbm9uZScgOiB1bmRlZmluZWQsXG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvUG9wcGVyQ29udGVudFByb3ZpZGVyPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuKTtcblxuUG9wcGVyQ29udGVudC5kaXNwbGF5TmFtZSA9IENPTlRFTlRfTkFNRTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogUG9wcGVyQXJyb3dcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgQVJST1dfTkFNRSA9ICdQb3BwZXJBcnJvdyc7XG5cbmNvbnN0IE9QUE9TSVRFX1NJREU6IFJlY29yZDxTaWRlLCBTaWRlPiA9IHtcbiAgdG9wOiAnYm90dG9tJyxcbiAgcmlnaHQ6ICdsZWZ0JyxcbiAgYm90dG9tOiAndG9wJyxcbiAgbGVmdDogJ3JpZ2h0Jyxcbn07XG5cbnR5cGUgUG9wcGVyQXJyb3dFbGVtZW50ID0gUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgQXJyb3dQcmltaXRpdmUuUm9vdD47XG50eXBlIEFycm93UHJvcHMgPSBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIEFycm93UHJpbWl0aXZlLlJvb3Q+O1xuaW50ZXJmYWNlIFBvcHBlckFycm93UHJvcHMgZXh0ZW5kcyBBcnJvd1Byb3BzIHt9XG5cbmNvbnN0IFBvcHBlckFycm93ID0gUmVhY3QuZm9yd2FyZFJlZjxQb3BwZXJBcnJvd0VsZW1lbnQsIFBvcHBlckFycm93UHJvcHM+KGZ1bmN0aW9uIFBvcHBlckFycm93KFxuICBwcm9wczogU2NvcGVkUHJvcHM8UG9wcGVyQXJyb3dQcm9wcz4sXG4gIGZvcndhcmRlZFJlZlxuKSB7XG4gIGNvbnN0IHsgX19zY29wZVBvcHBlciwgLi4uYXJyb3dQcm9wcyB9ID0gcHJvcHM7XG4gIGNvbnN0IGNvbnRlbnRDb250ZXh0ID0gdXNlQ29udGVudENvbnRleHQoQVJST1dfTkFNRSwgX19zY29wZVBvcHBlcik7XG4gIGNvbnN0IGJhc2VTaWRlID0gT1BQT1NJVEVfU0lERVtjb250ZW50Q29udGV4dC5wbGFjZWRTaWRlXTtcblxuICByZXR1cm4gKFxuICAgIC8vIHdlIGhhdmUgdG8gdXNlIGFuIGV4dHJhIHdyYXBwZXIgYmVjYXVzZSBgUmVzaXplT2JzZXJ2ZXJgICh1c2VkIGJ5IGB1c2VTaXplYClcbiAgICAvLyBkb2Vzbid0IHJlcG9ydCBzaXplIGFzIHdlJ2QgZXhwZWN0IG9uIFNWRyBlbGVtZW50cy5cbiAgICAvLyBpdCByZXBvcnRzIHRoZWlyIGJvdW5kaW5nIGJveCB3aGljaCBpcyBlZmZlY3RpdmVseSB0aGUgbGFyZ2VzdCBwYXRoIGluc2lkZSB0aGUgU1ZHLlxuICAgIDxzcGFuXG4gICAgICByZWY9e2NvbnRlbnRDb250ZXh0Lm9uQXJyb3dDaGFuZ2V9XG4gICAgICBzdHlsZT17e1xuICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgbGVmdDogY29udGVudENvbnRleHQuYXJyb3dYLFxuICAgICAgICB0b3A6IGNvbnRlbnRDb250ZXh0LmFycm93WSxcbiAgICAgICAgW2Jhc2VTaWRlXTogMCxcbiAgICAgICAgdHJhbnNmb3JtT3JpZ2luOiB7XG4gICAgICAgICAgdG9wOiAnJyxcbiAgICAgICAgICByaWdodDogJzAgMCcsXG4gICAgICAgICAgYm90dG9tOiAnY2VudGVyIDAnLFxuICAgICAgICAgIGxlZnQ6ICcxMDAlIDAnLFxuICAgICAgICB9W2NvbnRlbnRDb250ZXh0LnBsYWNlZFNpZGVdLFxuICAgICAgICB0cmFuc2Zvcm06IHtcbiAgICAgICAgICB0b3A6ICd0cmFuc2xhdGVZKDEwMCUpJyxcbiAgICAgICAgICByaWdodDogJ3RyYW5zbGF0ZVkoNTAlKSByb3RhdGUoOTBkZWcpIHRyYW5zbGF0ZVgoLTUwJSknLFxuICAgICAgICAgIGJvdHRvbTogYHJvdGF0ZSgxODBkZWcpYCxcbiAgICAgICAgICBsZWZ0OiAndHJhbnNsYXRlWSg1MCUpIHJvdGF0ZSgtOTBkZWcpIHRyYW5zbGF0ZVgoNTAlKScsXG4gICAgICAgIH1bY29udGVudENvbnRleHQucGxhY2VkU2lkZV0sXG4gICAgICAgIHZpc2liaWxpdHk6IGNvbnRlbnRDb250ZXh0LnNob3VsZEhpZGVBcnJvdyA/ICdoaWRkZW4nIDogdW5kZWZpbmVkLFxuICAgICAgfX1cbiAgICA+XG4gICAgICA8QXJyb3dQcmltaXRpdmUuUm9vdFxuICAgICAgICB7Li4uYXJyb3dQcm9wc31cbiAgICAgICAgcmVmPXtmb3J3YXJkZWRSZWZ9XG4gICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgLi4uYXJyb3dQcm9wcy5zdHlsZSxcbiAgICAgICAgICAvLyBlbnN1cmVzIHRoZSBlbGVtZW50IGNhbiBiZSBtZWFzdXJlZCBjb3JyZWN0bHkgKG1vc3RseSBmb3IgaWYgU1ZHKVxuICAgICAgICAgIGRpc3BsYXk6ICdibG9jaycsXG4gICAgICAgIH19XG4gICAgICAvPlxuICAgIDwvc3Bhbj5cbiAgKTtcbn0pO1xuXG5Qb3BwZXJBcnJvdy5kaXNwbGF5TmFtZSA9IEFSUk9XX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuZnVuY3Rpb24gaXNOb3ROdWxsPFQ+KHZhbHVlOiBUIHwgbnVsbCk6IHZhbHVlIGlzIFQge1xuICByZXR1cm4gdmFsdWUgIT09IG51bGw7XG59XG5cbmNvbnN0IHRyYW5zZm9ybU9yaWdpbiA9IChvcHRpb25zOiB7IGFycm93V2lkdGg6IG51bWJlcjsgYXJyb3dIZWlnaHQ6IG51bWJlciB9KTogTWlkZGxld2FyZSA9PiAoe1xuICBuYW1lOiAndHJhbnNmb3JtT3JpZ2luJyxcbiAgb3B0aW9ucyxcbiAgZm4oZGF0YSkge1xuICAgIGNvbnN0IHsgcGxhY2VtZW50LCByZWN0cywgbWlkZGxld2FyZURhdGEgfSA9IGRhdGE7XG5cbiAgICBjb25zdCBjYW5ub3RDZW50ZXJBcnJvdyA9IG1pZGRsZXdhcmVEYXRhLmFycm93Py5jZW50ZXJPZmZzZXQgIT09IDA7XG4gICAgY29uc3QgaXNBcnJvd0hpZGRlbiA9IGNhbm5vdENlbnRlckFycm93O1xuICAgIGNvbnN0IGFycm93V2lkdGggPSBpc0Fycm93SGlkZGVuID8gMCA6IG9wdGlvbnMuYXJyb3dXaWR0aDtcbiAgICBjb25zdCBhcnJvd0hlaWdodCA9IGlzQXJyb3dIaWRkZW4gPyAwIDogb3B0aW9ucy5hcnJvd0hlaWdodDtcblxuICAgIGNvbnN0IFtwbGFjZWRTaWRlLCBwbGFjZWRBbGlnbl0gPSBnZXRTaWRlQW5kQWxpZ25Gcm9tUGxhY2VtZW50KHBsYWNlbWVudCk7XG4gICAgY29uc3Qgbm9BcnJvd0FsaWduID0geyBzdGFydDogJzAlJywgY2VudGVyOiAnNTAlJywgZW5kOiAnMTAwJScgfVtwbGFjZWRBbGlnbl07XG5cbiAgICBjb25zdCBhcnJvd1hDZW50ZXIgPSAobWlkZGxld2FyZURhdGEuYXJyb3c/LnggPz8gMCkgKyBhcnJvd1dpZHRoIC8gMjtcbiAgICBjb25zdCBhcnJvd1lDZW50ZXIgPSAobWlkZGxld2FyZURhdGEuYXJyb3c/LnkgPz8gMCkgKyBhcnJvd0hlaWdodCAvIDI7XG5cbiAgICBsZXQgeCA9ICcnO1xuICAgIGxldCB5ID0gJyc7XG5cbiAgICBpZiAocGxhY2VkU2lkZSA9PT0gJ2JvdHRvbScpIHtcbiAgICAgIHggPSBpc0Fycm93SGlkZGVuID8gbm9BcnJvd0FsaWduIDogYCR7YXJyb3dYQ2VudGVyfXB4YDtcbiAgICAgIHkgPSBgJHstYXJyb3dIZWlnaHR9cHhgO1xuICAgIH0gZWxzZSBpZiAocGxhY2VkU2lkZSA9PT0gJ3RvcCcpIHtcbiAgICAgIHggPSBpc0Fycm93SGlkZGVuID8gbm9BcnJvd0FsaWduIDogYCR7YXJyb3dYQ2VudGVyfXB4YDtcbiAgICAgIHkgPSBgJHtyZWN0cy5mbG9hdGluZy5oZWlnaHQgKyBhcnJvd0hlaWdodH1weGA7XG4gICAgfSBlbHNlIGlmIChwbGFjZWRTaWRlID09PSAncmlnaHQnKSB7XG4gICAgICB4ID0gYCR7LWFycm93SGVpZ2h0fXB4YDtcbiAgICAgIHkgPSBpc0Fycm93SGlkZGVuID8gbm9BcnJvd0FsaWduIDogYCR7YXJyb3dZQ2VudGVyfXB4YDtcbiAgICB9IGVsc2UgaWYgKHBsYWNlZFNpZGUgPT09ICdsZWZ0Jykge1xuICAgICAgeCA9IGAke3JlY3RzLmZsb2F0aW5nLndpZHRoICsgYXJyb3dIZWlnaHR9cHhgO1xuICAgICAgeSA9IGlzQXJyb3dIaWRkZW4gPyBub0Fycm93QWxpZ24gOiBgJHthcnJvd1lDZW50ZXJ9cHhgO1xuICAgIH1cbiAgICByZXR1cm4geyBkYXRhOiB7IHgsIHkgfSB9O1xuICB9LFxufSk7XG5cbmZ1bmN0aW9uIGdldFNpZGVBbmRBbGlnbkZyb21QbGFjZW1lbnQocGxhY2VtZW50OiBQbGFjZW1lbnQpIHtcbiAgY29uc3QgW3NpZGUsIGFsaWduID0gJ2NlbnRlciddID0gcGxhY2VtZW50LnNwbGl0KCctJyk7XG4gIHJldHVybiBbc2lkZSBhcyBTaWRlLCBhbGlnbiBhcyBBbGlnbl0gYXMgY29uc3Q7XG59XG5cbmNvbnN0IFJvb3QgPSBQb3BwZXI7XG5jb25zdCBBbmNob3IgPSBQb3BwZXJBbmNob3I7XG5jb25zdCBDb250ZW50ID0gUG9wcGVyQ29udGVudDtcbmNvbnN0IEFycm93ID0gUG9wcGVyQXJyb3c7XG5cbmV4cG9ydCB7XG4gIGNyZWF0ZVBvcHBlclNjb3BlLFxuICAvL1xuICBQb3BwZXIsXG4gIFBvcHBlckFuY2hvcixcbiAgUG9wcGVyQ29udGVudCxcbiAgUG9wcGVyQXJyb3csXG4gIC8vXG4gIFJvb3QsXG4gIEFuY2hvcixcbiAgQ29udGVudCxcbiAgQXJyb3csXG4gIC8vXG4gIFNJREVfT1BUSU9OUyxcbiAgQUxJR05fT1BUSU9OUyxcbn07XG5leHBvcnQgdHlwZSB7IFBvcHBlclByb3BzLCBQb3BwZXJBbmNob3JQcm9wcywgUG9wcGVyQ29udGVudFByb3BzLCBQb3BwZXJBcnJvd1Byb3BzIH07XG4iXSwibmFtZXMiOlsiUG9wcGVyQXJyb3ciLCJSb290Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // packages/react/portal/src/Portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)({\n        \"Portal.useLayoutEffect\": ()=>setMounted(true)\n    }[\"Portal.useLayoutEffect\"], []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/primitive/src/Primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-select/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-select/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   ItemText: () => (/* binding */ ItemText),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   ScrollDownButton: () => (/* binding */ ScrollDownButton),\n/* harmony export */   ScrollUpButton: () => (/* binding */ ScrollUpButton),\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectArrow: () => (/* binding */ SelectArrow),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectIcon: () => (/* binding */ SelectIcon),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectItemIndicator: () => (/* binding */ SelectItemIndicator),\n/* harmony export */   SelectItemText: () => (/* binding */ SelectItemText),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectPortal: () => (/* binding */ SelectPortal),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue),\n/* harmony export */   SelectViewport: () => (/* binding */ SelectViewport),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   Value: () => (/* binding */ Value),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createSelectScope: () => (/* binding */ createSelectScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Group,Icon,Item,ItemIndicator,ItemText,Label,Portal,Root,ScrollDownButton,ScrollUpButton,Select,SelectArrow,SelectContent,SelectGroup,SelectIcon,SelectItem,SelectItemIndicator,SelectItemText,SelectLabel,SelectPortal,SelectScrollDownButton,SelectScrollUpButton,SelectSeparator,SelectTrigger,SelectValue,SelectViewport,Separator,Trigger,Value,Viewport,createSelectScope auto */ // packages/react/select/src/Select.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar OPEN_KEYS = [\n    \" \",\n    \"Enter\",\n    \"ArrowUp\",\n    \"ArrowDown\"\n];\nvar SELECTION_KEYS = [\n    \" \",\n    \"Enter\"\n];\nvar SELECT_NAME = \"Select\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(SELECT_NAME);\nvar [createSelectContext, createSelectScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(SELECT_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope)();\nvar [SelectProvider, useSelectContext] = createSelectContext(SELECT_NAME);\nvar [SelectNativeOptionsProvider, useSelectNativeOptionsContext] = createSelectContext(SELECT_NAME);\nvar Select = (props)=>{\n    const { __scopeSelect, children, open: openProp, defaultOpen, onOpenChange, value: valueProp, defaultValue, onValueChange, dir, name, autoComplete, disabled, required, form } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNode, setValueNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNodeHasChildren, setValueNodeHasChildren] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue,\n        onChange: onValueChange\n    });\n    const triggerPointerDownPosRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFormControl = trigger ? form || !!trigger.closest(\"form\") : true;\n    const [nativeOptionsSet, setNativeOptionsSet] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Set());\n    const nativeSelectKey = Array.from(nativeOptionsSet).map((option)=>option.props.value).join(\";\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(SelectProvider, {\n            required,\n            scope: __scopeSelect,\n            trigger,\n            onTriggerChange: setTrigger,\n            valueNode,\n            onValueNodeChange: setValueNode,\n            valueNodeHasChildren,\n            onValueNodeHasChildrenChange: setValueNodeHasChildren,\n            contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)(),\n            value,\n            onValueChange: setValue,\n            open,\n            onOpenChange: setOpen,\n            dir: direction,\n            triggerPointerDownPosRef,\n            disabled,\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n                    scope: __scopeSelect,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectNativeOptionsProvider, {\n                        scope: props.__scopeSelect,\n                        onNativeOptionAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                            \"Select.useCallback\": (option)=>{\n                                setNativeOptionsSet({\n                                    \"Select.useCallback\": (prev)=>new Set(prev).add(option)\n                                }[\"Select.useCallback\"]);\n                            }\n                        }[\"Select.useCallback\"], []),\n                        onNativeOptionRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                            \"Select.useCallback\": (option)=>{\n                                setNativeOptionsSet({\n                                    \"Select.useCallback\": (prev)=>{\n                                        const optionsSet = new Set(prev);\n                                        optionsSet.delete(option);\n                                        return optionsSet;\n                                    }\n                                }[\"Select.useCallback\"]);\n                            }\n                        }[\"Select.useCallback\"], []),\n                        children\n                    })\n                }),\n                isFormControl ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(BubbleSelect, {\n                    \"aria-hidden\": true,\n                    required,\n                    tabIndex: -1,\n                    name,\n                    autoComplete,\n                    value,\n                    onChange: (event)=>setValue(event.target.value),\n                    disabled,\n                    form,\n                    children: [\n                        value === void 0 ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n                            value: \"\"\n                        }) : null,\n                        Array.from(nativeOptionsSet)\n                    ]\n                }, nativeSelectKey) : null\n            ]\n        })\n    });\n};\nSelect.displayName = SELECT_NAME;\nvar TRIGGER_NAME = \"SelectTrigger\";\nvar SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch({\n        \"SelectTrigger.useTypeaheadSearch\": (search)=>{\n            const enabledItems = getItems().filter({\n                \"SelectTrigger.useTypeaheadSearch.enabledItems\": (item)=>!item.disabled\n            }[\"SelectTrigger.useTypeaheadSearch.enabledItems\"]);\n            const currentItem = enabledItems.find({\n                \"SelectTrigger.useTypeaheadSearch.currentItem\": (item)=>item.value === context.value\n            }[\"SelectTrigger.useTypeaheadSearch.currentItem\"]);\n            const nextItem = findNextItem(enabledItems, search, currentItem);\n            if (nextItem !== void 0) {\n                context.onValueChange(nextItem.value);\n            }\n        }\n    }[\"SelectTrigger.useTypeaheadSearch\"]);\n    const handleOpen = (pointerEvent)=>{\n        if (!isDisabled) {\n            context.onOpenChange(true);\n            resetTypeahead();\n        }\n        if (pointerEvent) {\n            context.triggerPointerDownPosRef.current = {\n                x: Math.round(pointerEvent.pageX),\n                y: Math.round(pointerEvent.pageY)\n            };\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.button, {\n            type: \"button\",\n            role: \"combobox\",\n            \"aria-controls\": context.contentId,\n            \"aria-expanded\": context.open,\n            \"aria-required\": context.required,\n            \"aria-autocomplete\": \"none\",\n            dir: context.dir,\n            \"data-state\": context.open ? \"open\" : \"closed\",\n            disabled: isDisabled,\n            \"data-disabled\": isDisabled ? \"\" : void 0,\n            \"data-placeholder\": shouldShowPlaceholder(context.value) ? \"\" : void 0,\n            ...triggerProps,\n            ref: composedRefs,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onClick, (event)=>{\n                event.currentTarget.focus();\n                if (pointerTypeRef.current !== \"mouse\") {\n                    handleOpen(event);\n                }\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onPointerDown, (event)=>{\n                pointerTypeRef.current = event.pointerType;\n                const target = event.target;\n                if (target.hasPointerCapture(event.pointerId)) {\n                    target.releasePointerCapture(event.pointerId);\n                }\n                if (event.button === 0 && event.ctrlKey === false && event.pointerType === \"mouse\") {\n                    handleOpen(event);\n                    event.preventDefault();\n                }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onKeyDown, (event)=>{\n                const isTypingAhead = searchRef.current !== \"\";\n                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                if (isTypingAhead && event.key === \" \") return;\n                if (OPEN_KEYS.includes(event.key)) {\n                    handleOpen();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nSelectTrigger.displayName = TRIGGER_NAME;\nvar VALUE_NAME = \"SelectValue\";\nvar SelectValue = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, children, placeholder = \"\", ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== void 0;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onValueNodeChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectValue.useLayoutEffect\": ()=>{\n            onValueNodeHasChildrenChange(hasChildren);\n        }\n    }[\"SelectValue.useLayoutEffect\"], [\n        onValueNodeHasChildrenChange,\n        hasChildren\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        ...valueProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: \"none\"\n        },\n        children: shouldShowPlaceholder(context.value) ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n            children: placeholder\n        }) : children\n    });\n});\nSelectValue.displayName = VALUE_NAME;\nvar ICON_NAME = \"SelectIcon\";\nvar SelectIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, children, ...iconProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...iconProps,\n        ref: forwardedRef,\n        children: children || \"\\u25BC\"\n    });\n});\nSelectIcon.displayName = ICON_NAME;\nvar PORTAL_NAME = \"SelectPortal\";\nvar SelectPortal = (props)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        ...props\n    });\n};\nSelectPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"SelectContent\";\nvar SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectContent.useLayoutEffect\": ()=>{\n            setFragment(new DocumentFragment());\n        }\n    }[\"SelectContent.useLayoutEffect\"], []);\n    if (!context.open) {\n        const frag = fragment;\n        return frag ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n            scope: props.__scopeSelect,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: props.__scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n                    children: props.children\n                })\n            })\n        }), frag) : null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentImpl, {\n        ...props,\n        ref: forwardedRef\n    });\n});\nSelectContent.displayName = CONTENT_NAME;\nvar CONTENT_MARGIN = 10;\nvar [SelectContentProvider, useSelectContentContext] = createSelectContext(CONTENT_NAME);\nvar CONTENT_IMPL_NAME = \"SelectContentImpl\";\nvar SelectContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, position = \"item-aligned\", onCloseAutoFocus, onEscapeKeyDown, onPointerDownOutside, //\n    // PopperContent props\n    side, sideOffset, align, alignOffset, arrowPadding, collisionBoundary, collisionPadding, sticky, hideWhenDetached, avoidCollisions, //\n    ...contentProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectContentImpl.useComposedRefs[composedRefs]\": (node)=>setContent(node)\n    }[\"SelectContentImpl.useComposedRefs[composedRefs]\"]);\n    const [selectedItem, setSelectedItem] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [selectedItemText, setSelectedItemText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const firstValidItemFoundRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_14__.hideOthers)(content);\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        content\n    ]);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__.useFocusGuards)();\n    const focusFirst = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[focusFirst]\": (candidates)=>{\n            const [firstItem, ...restItems] = getItems().map({\n                \"SelectContentImpl.useCallback[focusFirst]\": (item)=>item.ref.current\n            }[\"SelectContentImpl.useCallback[focusFirst]\"]);\n            const [lastItem] = restItems.slice(-1);\n            const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n            for (const candidate of candidates){\n                if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n                candidate?.scrollIntoView({\n                    block: \"nearest\"\n                });\n                if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n                if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n                candidate?.focus();\n                if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n            }\n        }\n    }[\"SelectContentImpl.useCallback[focusFirst]\"], [\n        getItems,\n        viewport\n    ]);\n    const focusSelectedItem = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[focusSelectedItem]\": ()=>focusFirst([\n                selectedItem,\n                content\n            ])\n    }[\"SelectContentImpl.useCallback[focusSelectedItem]\"], [\n        focusFirst,\n        selectedItem,\n        content\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            if (isPositioned) {\n                focusSelectedItem();\n            }\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        isPositioned,\n        focusSelectedItem\n    ]);\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            if (content) {\n                let pointerMoveDelta = {\n                    x: 0,\n                    y: 0\n                };\n                const handlePointerMove = {\n                    \"SelectContentImpl.useEffect.handlePointerMove\": (event)=>{\n                        pointerMoveDelta = {\n                            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n                            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0))\n                        };\n                    }\n                }[\"SelectContentImpl.useEffect.handlePointerMove\"];\n                const handlePointerUp = {\n                    \"SelectContentImpl.useEffect.handlePointerUp\": (event)=>{\n                        if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n                            event.preventDefault();\n                        } else {\n                            if (!content.contains(event.target)) {\n                                onOpenChange(false);\n                            }\n                        }\n                        document.removeEventListener(\"pointermove\", handlePointerMove);\n                        triggerPointerDownPosRef.current = null;\n                    }\n                }[\"SelectContentImpl.useEffect.handlePointerUp\"];\n                if (triggerPointerDownPosRef.current !== null) {\n                    document.addEventListener(\"pointermove\", handlePointerMove);\n                    document.addEventListener(\"pointerup\", handlePointerUp, {\n                        capture: true,\n                        once: true\n                    });\n                }\n                return ({\n                    \"SelectContentImpl.useEffect\": ()=>{\n                        document.removeEventListener(\"pointermove\", handlePointerMove);\n                        document.removeEventListener(\"pointerup\", handlePointerUp, {\n                            capture: true\n                        });\n                    }\n                })[\"SelectContentImpl.useEffect\"];\n            }\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        content,\n        onOpenChange,\n        triggerPointerDownPosRef\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectContentImpl.useEffect\": ()=>{\n            const close = {\n                \"SelectContentImpl.useEffect.close\": ()=>onOpenChange(false)\n            }[\"SelectContentImpl.useEffect.close\"];\n            window.addEventListener(\"blur\", close);\n            window.addEventListener(\"resize\", close);\n            return ({\n                \"SelectContentImpl.useEffect\": ()=>{\n                    window.removeEventListener(\"blur\", close);\n                    window.removeEventListener(\"resize\", close);\n                }\n            })[\"SelectContentImpl.useEffect\"];\n        }\n    }[\"SelectContentImpl.useEffect\"], [\n        onOpenChange\n    ]);\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch({\n        \"SelectContentImpl.useTypeaheadSearch\": (search)=>{\n            const enabledItems = getItems().filter({\n                \"SelectContentImpl.useTypeaheadSearch.enabledItems\": (item)=>!item.disabled\n            }[\"SelectContentImpl.useTypeaheadSearch.enabledItems\"]);\n            const currentItem = enabledItems.find({\n                \"SelectContentImpl.useTypeaheadSearch.currentItem\": (item)=>item.ref.current === document.activeElement\n            }[\"SelectContentImpl.useTypeaheadSearch.currentItem\"]);\n            const nextItem = findNextItem(enabledItems, search, currentItem);\n            if (nextItem) {\n                setTimeout({\n                    \"SelectContentImpl.useTypeaheadSearch\": ()=>nextItem.ref.current.focus()\n                }[\"SelectContentImpl.useTypeaheadSearch\"]);\n            }\n        }\n    }[\"SelectContentImpl.useTypeaheadSearch\"]);\n    const itemRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[itemRefCallback]\": (node, value, disabled)=>{\n            const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n            const isSelectedItem = context.value !== void 0 && context.value === value;\n            if (isSelectedItem || isFirstValidItem) {\n                setSelectedItem(node);\n                if (isFirstValidItem) firstValidItemFoundRef.current = true;\n            }\n        }\n    }[\"SelectContentImpl.useCallback[itemRefCallback]\"], [\n        context.value\n    ]);\n    const handleItemLeave = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[handleItemLeave]\": ()=>content?.focus()\n    }[\"SelectContentImpl.useCallback[handleItemLeave]\"], [\n        content\n    ]);\n    const itemTextRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectContentImpl.useCallback[itemTextRefCallback]\": (node, value, disabled)=>{\n            const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n            const isSelectedItem = context.value !== void 0 && context.value === value;\n            if (isSelectedItem || isFirstValidItem) {\n                setSelectedItemText(node);\n            }\n        }\n    }[\"SelectContentImpl.useCallback[itemTextRefCallback]\"], [\n        context.value\n    ]);\n    const SelectPosition = position === \"popper\" ? SelectPopperPosition : SelectItemAlignedPosition;\n    const popperContentProps = SelectPosition === SelectPopperPosition ? {\n        side,\n        sideOffset,\n        align,\n        alignOffset,\n        arrowPadding,\n        collisionBoundary,\n        collisionPadding,\n        sticky,\n        hideWhenDetached,\n        avoidCollisions\n    } : {};\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n        scope: __scopeSelect,\n        content,\n        viewport,\n        onViewportChange: setViewport,\n        itemRefCallback,\n        selectedItem,\n        onItemLeave: handleItemLeave,\n        itemTextRefCallback,\n        focusSelectedItem,\n        selectedItemText,\n        position,\n        isPositioned,\n        searchRef,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_17__.Slot,\n            allowPinchZoom: true,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__.FocusScope, {\n                asChild: true,\n                trapped: context.open,\n                onMountAutoFocus: (event)=>{\n                    event.preventDefault();\n                },\n                onUnmountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(onCloseAutoFocus, (event)=>{\n                    context.trigger?.focus({\n                        preventScroll: true\n                    });\n                    event.preventDefault();\n                }),\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents: true,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside: (event)=>event.preventDefault(),\n                    onDismiss: ()=>context.onOpenChange(false),\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectPosition, {\n                        role: \"listbox\",\n                        id: context.contentId,\n                        \"data-state\": context.open ? \"open\" : \"closed\",\n                        dir: context.dir,\n                        onContextMenu: (event)=>event.preventDefault(),\n                        ...contentProps,\n                        ...popperContentProps,\n                        onPlaced: ()=>setIsPositioned(true),\n                        ref: composedRefs,\n                        style: {\n                            // flex layout so we can place the scroll buttons properly\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            // reset the outline by default as the content MAY get focused\n                            outline: \"none\",\n                            ...contentProps.style\n                        },\n                        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                            if (event.key === \"Tab\") event.preventDefault();\n                            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                            if ([\n                                \"ArrowUp\",\n                                \"ArrowDown\",\n                                \"Home\",\n                                \"End\"\n                            ].includes(event.key)) {\n                                const items = getItems().filter((item)=>!item.disabled);\n                                let candidateNodes = items.map((item)=>item.ref.current);\n                                if ([\n                                    \"ArrowUp\",\n                                    \"End\"\n                                ].includes(event.key)) {\n                                    candidateNodes = candidateNodes.slice().reverse();\n                                }\n                                if ([\n                                    \"ArrowUp\",\n                                    \"ArrowDown\"\n                                ].includes(event.key)) {\n                                    const currentElement = event.target;\n                                    const currentIndex = candidateNodes.indexOf(currentElement);\n                                    candidateNodes = candidateNodes.slice(currentIndex + 1);\n                                }\n                                setTimeout(()=>focusFirst(candidateNodes));\n                                event.preventDefault();\n                            }\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\nvar ITEM_ALIGNED_POSITION_NAME = \"SelectItemAlignedPosition\";\nvar SelectItemAlignedPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onPlaced, ...popperProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n    const [contentWrapper, setContentWrapper] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectItemAlignedPosition.useComposedRefs[composedRefs]\": (node)=>setContent(node)\n    }[\"SelectItemAlignedPosition.useComposedRefs[composedRefs]\"]);\n    const getItems = useCollection(__scopeSelect);\n    const shouldExpandOnScrollRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const shouldRepositionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n    const position = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectItemAlignedPosition.useCallback[position]\": ()=>{\n            if (context.trigger && context.valueNode && contentWrapper && content && viewport && selectedItem && selectedItemText) {\n                const triggerRect = context.trigger.getBoundingClientRect();\n                const contentRect = content.getBoundingClientRect();\n                const valueNodeRect = context.valueNode.getBoundingClientRect();\n                const itemTextRect = selectedItemText.getBoundingClientRect();\n                if (context.dir !== \"rtl\") {\n                    const itemTextOffset = itemTextRect.left - contentRect.left;\n                    const left = valueNodeRect.left - itemTextOffset;\n                    const leftDelta = triggerRect.left - left;\n                    const minContentWidth = triggerRect.width + leftDelta;\n                    const contentWidth = Math.max(minContentWidth, contentRect.width);\n                    const rightEdge = window.innerWidth - CONTENT_MARGIN;\n                    const clampedLeft = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(left, [\n                        CONTENT_MARGIN,\n                        // Prevents the content from going off the starting edge of the\n                        // viewport. It may still go off the ending edge, but this can be\n                        // controlled by the user since they may want to manage overflow in a\n                        // specific way.\n                        // https://github.com/radix-ui/primitives/issues/2049\n                        Math.max(CONTENT_MARGIN, rightEdge - contentWidth)\n                    ]);\n                    contentWrapper.style.minWidth = minContentWidth + \"px\";\n                    contentWrapper.style.left = clampedLeft + \"px\";\n                } else {\n                    const itemTextOffset = contentRect.right - itemTextRect.right;\n                    const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n                    const rightDelta = window.innerWidth - triggerRect.right - right;\n                    const minContentWidth = triggerRect.width + rightDelta;\n                    const contentWidth = Math.max(minContentWidth, contentRect.width);\n                    const leftEdge = window.innerWidth - CONTENT_MARGIN;\n                    const clampedRight = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(right, [\n                        CONTENT_MARGIN,\n                        Math.max(CONTENT_MARGIN, leftEdge - contentWidth)\n                    ]);\n                    contentWrapper.style.minWidth = minContentWidth + \"px\";\n                    contentWrapper.style.right = clampedRight + \"px\";\n                }\n                const items = getItems();\n                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                const itemsHeight = viewport.scrollHeight;\n                const contentStyles = window.getComputedStyle(content);\n                const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n                const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n                const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n                const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n                const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth;\n                const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n                const viewportStyles = window.getComputedStyle(viewport);\n                const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n                const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n                const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n                const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n                const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n                const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n                const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n                const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n                const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n                if (willAlignWithoutTopOverflow) {\n                    const isLastItem = items.length > 0 && selectedItem === items[items.length - 1].ref.current;\n                    contentWrapper.style.bottom = \"0px\";\n                    const viewportOffsetBottom = content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n                    const clampedTriggerMiddleToBottomEdge = Math.max(triggerMiddleToBottomEdge, selectedItemHalfHeight + // viewport might have padding bottom, include it to avoid a scrollable viewport\n                    (isLastItem ? viewportPaddingBottom : 0) + viewportOffsetBottom + contentBorderBottomWidth);\n                    const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n                    contentWrapper.style.height = height + \"px\";\n                } else {\n                    const isFirstItem = items.length > 0 && selectedItem === items[0].ref.current;\n                    contentWrapper.style.top = \"0px\";\n                    const clampedTopEdgeToTriggerMiddle = Math.max(topEdgeToTriggerMiddle, contentBorderTopWidth + viewport.offsetTop + // viewport might have padding top, include it to avoid a scrollable viewport\n                    (isFirstItem ? viewportPaddingTop : 0) + selectedItemHalfHeight);\n                    const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n                    contentWrapper.style.height = height + \"px\";\n                    viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n                }\n                contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n                contentWrapper.style.minHeight = minContentHeight + \"px\";\n                contentWrapper.style.maxHeight = availableHeight + \"px\";\n                onPlaced?.();\n                requestAnimationFrame({\n                    \"SelectItemAlignedPosition.useCallback[position]\": ()=>shouldExpandOnScrollRef.current = true\n                }[\"SelectItemAlignedPosition.useCallback[position]\"]);\n            }\n        }\n    }[\"SelectItemAlignedPosition.useCallback[position]\"], [\n        getItems,\n        context.trigger,\n        context.valueNode,\n        contentWrapper,\n        content,\n        viewport,\n        selectedItem,\n        selectedItemText,\n        context.dir,\n        onPlaced\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectItemAlignedPosition.useLayoutEffect\": ()=>position()\n    }[\"SelectItemAlignedPosition.useLayoutEffect\"], [\n        position\n    ]);\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectItemAlignedPosition.useLayoutEffect\": ()=>{\n            if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n        }\n    }[\"SelectItemAlignedPosition.useLayoutEffect\"], [\n        content\n    ]);\n    const handleScrollButtonChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectItemAlignedPosition.useCallback[handleScrollButtonChange]\": (node)=>{\n            if (node && shouldRepositionRef.current === true) {\n                position();\n                focusSelectedItem?.();\n                shouldRepositionRef.current = false;\n            }\n        }\n    }[\"SelectItemAlignedPosition.useCallback[handleScrollButtonChange]\"], [\n        position,\n        focusSelectedItem\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectViewportProvider, {\n        scope: __scopeSelect,\n        contentWrapper,\n        shouldExpandOnScrollRef,\n        onScrollButtonChange: handleScrollButtonChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n            ref: setContentWrapper,\n            style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                position: \"fixed\",\n                zIndex: contentZIndex\n            },\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                ...popperProps,\n                ref: composedRefs,\n                style: {\n                    // When we get the height of the content, it includes borders. If we were to set\n                    // the height without having `boxSizing: 'border-box'` it would be too big.\n                    boxSizing: \"border-box\",\n                    // We need to ensure the content doesn't get taller than the wrapper\n                    maxHeight: \"100%\",\n                    ...popperProps.style\n                }\n            })\n        })\n    });\n});\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\nvar POPPER_POSITION_NAME = \"SelectPopperPosition\";\nvar SelectPopperPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, align = \"start\", collisionPadding = CONTENT_MARGIN, ...popperProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Content, {\n        ...popperScope,\n        ...popperProps,\n        ref: forwardedRef,\n        align,\n        collisionPadding,\n        style: {\n            // Ensure border-box for floating-ui calculations\n            boxSizing: \"border-box\",\n            ...popperProps.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-select-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-select-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-select-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-select-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-select-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\nvar [SelectViewportProvider, useSelectViewportContext] = createSelectContext(CONTENT_NAME, {});\nvar VIEWPORT_NAME = \"SelectViewport\";\nvar SelectViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                    \"data-radix-select-viewport\": \"\",\n                    role: \"presentation\",\n                    ...viewportProps,\n                    ref: composedRefs,\n                    style: {\n                        // we use position: 'relative' here on the `viewport` so that when we call\n                        // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n                        // (independent of the scrollUpButton).\n                        position: \"relative\",\n                        flex: 1,\n                        // Viewport should only be scrollable in the vertical direction.\n                        // This won't work in vertical writing modes, so we'll need to\n                        // revisit this if/when that is supported\n                        // https://developer.chrome.com/blog/vertical-form-controls\n                        overflow: \"hidden auto\",\n                        ...viewportProps.style\n                    },\n                    onScroll: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(viewportProps.onScroll, (event)=>{\n                        const viewport = event.currentTarget;\n                        const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n                        if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                            const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                            if (scrolledBy > 0) {\n                                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                                const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                                const cssHeight = parseFloat(contentWrapper.style.height);\n                                const prevHeight = Math.max(cssMinHeight, cssHeight);\n                                if (prevHeight < availableHeight) {\n                                    const nextHeight = prevHeight + scrolledBy;\n                                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                                    const heightDiff = nextHeight - clampedNextHeight;\n                                    contentWrapper.style.height = clampedNextHeight + \"px\";\n                                    if (contentWrapper.style.bottom === \"0px\") {\n                                        viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                                        contentWrapper.style.justifyContent = \"flex-end\";\n                                    }\n                                }\n                            }\n                        }\n                        prevScrollTopRef.current = viewport.scrollTop;\n                    })\n                })\n            })\n        ]\n    });\n});\nSelectViewport.displayName = VIEWPORT_NAME;\nvar GROUP_NAME = \"SelectGroup\";\nvar [SelectGroupContextProvider, useSelectGroupContext] = createSelectContext(GROUP_NAME);\nvar SelectGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectGroupContextProvider, {\n        scope: __scopeSelect,\n        id: groupId,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n            role: \"group\",\n            \"aria-labelledby\": groupId,\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nSelectGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"SelectLabel\";\nvar SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        id: groupContext.id,\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nSelectLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"SelectItem\";\nvar [SelectItemContextProvider, useSelectItemContext] = createSelectContext(ITEM_NAME);\nvar SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, value, disabled = false, textValue: textValueProp, ...itemProps } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(textValueProp ?? \"\");\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectItem.useComposedRefs[composedRefs]\": (node)=>contentContext.itemRefCallback?.(node, value, disabled)\n    }[\"SelectItem.useComposedRefs[composedRefs]\"]);\n    const textId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const handleSelect = ()=>{\n        if (!disabled) {\n            context.onValueChange(value);\n            context.onOpenChange(false);\n        }\n    };\n    if (value === \"\") {\n        throw new Error(\"A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\");\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectItemContextProvider, {\n        scope: __scopeSelect,\n        value,\n        disabled,\n        textId,\n        isSelected,\n        onItemTextChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"SelectItem.useCallback\": (node)=>{\n                setTextValue({\n                    \"SelectItem.useCallback\": (prevTextValue)=>prevTextValue || (node?.textContent ?? \"\").trim()\n                }[\"SelectItem.useCallback\"]);\n            }\n        }[\"SelectItem.useCallback\"], []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n            scope: __scopeSelect,\n            value,\n            disabled,\n            textValue,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                role: \"option\",\n                \"aria-labelledby\": textId,\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-selected\": isSelected && isFocused,\n                \"data-state\": isSelected ? \"checked\" : \"unchecked\",\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                tabIndex: disabled ? void 0 : -1,\n                ...itemProps,\n                ref: composedRefs,\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onBlur, ()=>setIsFocused(false)),\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onClick, ()=>{\n                    if (pointerTypeRef.current !== \"mouse\") handleSelect();\n                }),\n                onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerUp, ()=>{\n                    if (pointerTypeRef.current === \"mouse\") handleSelect();\n                }),\n                onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerDown, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                }),\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerMove, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                    if (disabled) {\n                        contentContext.onItemLeave?.();\n                    } else if (pointerTypeRef.current === \"mouse\") {\n                        event.currentTarget.focus({\n                            preventScroll: true\n                        });\n                    }\n                }),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerLeave, (event)=>{\n                    if (event.currentTarget === document.activeElement) {\n                        contentContext.onItemLeave?.();\n                    }\n                }),\n                onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onKeyDown, (event)=>{\n                    const isTypingAhead = contentContext.searchRef?.current !== \"\";\n                    if (isTypingAhead && event.key === \" \") return;\n                    if (SELECTION_KEYS.includes(event.key)) handleSelect();\n                    if (event.key === \" \") event.preventDefault();\n                })\n            })\n        })\n    });\n});\nSelectItem.displayName = ITEM_NAME;\nvar ITEM_TEXT_NAME = \"SelectItemText\";\nvar SelectItemText = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, {\n        \"SelectItemText.useComposedRefs[composedRefs]\": (node)=>setItemTextNode(node)\n    }[\"SelectItemText.useComposedRefs[composedRefs]\"], itemContext.onItemTextChange, {\n        \"SelectItemText.useComposedRefs[composedRefs]\": (node)=>contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    }[\"SelectItemText.useComposedRefs[composedRefs]\"]);\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"SelectItemText.useMemo[nativeOption]\": ()=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n                value: itemContext.value,\n                disabled: itemContext.disabled,\n                children: textContent\n            }, itemContext.value)\n    }[\"SelectItemText.useMemo[nativeOption]\"], [\n        itemContext.disabled,\n        itemContext.value,\n        textContent\n    ]);\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectItemText.useLayoutEffect\": ()=>{\n            onNativeOptionAdd(nativeOption);\n            return ({\n                \"SelectItemText.useLayoutEffect\": ()=>onNativeOptionRemove(nativeOption)\n            })[\"SelectItemText.useLayoutEffect\"];\n        }\n    }[\"SelectItemText.useLayoutEffect\"], [\n        onNativeOptionAdd,\n        onNativeOptionRemove,\n        nativeOption\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n                id: itemContext.textId,\n                ...itemTextProps,\n                ref: composedRefs\n            }),\n            itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(itemTextProps.children, context.valueNode) : null\n        ]\n    });\n});\nSelectItemText.displayName = ITEM_TEXT_NAME;\nvar ITEM_INDICATOR_NAME = \"SelectItemIndicator\";\nvar SelectItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...itemIndicatorProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SCROLL_UP_BUTTON_NAME = \"SelectScrollUpButton\";\nvar SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollUp, setCanScrollUp] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectScrollUpButton.useLayoutEffect\": ()=>{\n            if (contentContext.viewport && contentContext.isPositioned) {\n                let handleScroll2 = {\n                    \"SelectScrollUpButton.useLayoutEffect.handleScroll2\": function() {\n                        const canScrollUp2 = viewport.scrollTop > 0;\n                        setCanScrollUp(canScrollUp2);\n                    }\n                }[\"SelectScrollUpButton.useLayoutEffect.handleScroll2\"];\n                var handleScroll = handleScroll2;\n                const viewport = contentContext.viewport;\n                handleScroll2();\n                viewport.addEventListener(\"scroll\", handleScroll2);\n                return ({\n                    \"SelectScrollUpButton.useLayoutEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll2)\n                })[\"SelectScrollUpButton.useLayoutEffect\"];\n            }\n        }\n    }[\"SelectScrollUpButton.useLayoutEffect\"], [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollUp ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\nvar SCROLL_DOWN_BUTTON_NAME = \"SelectScrollDownButton\";\nvar SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollDown, setCanScrollDown] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectScrollDownButton.useLayoutEffect\": ()=>{\n            if (contentContext.viewport && contentContext.isPositioned) {\n                let handleScroll2 = {\n                    \"SelectScrollDownButton.useLayoutEffect.handleScroll2\": function() {\n                        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n                        const canScrollDown2 = Math.ceil(viewport.scrollTop) < maxScroll;\n                        setCanScrollDown(canScrollDown2);\n                    }\n                }[\"SelectScrollDownButton.useLayoutEffect.handleScroll2\"];\n                var handleScroll = handleScroll2;\n                const viewport = contentContext.viewport;\n                handleScroll2();\n                viewport.addEventListener(\"scroll\", handleScroll2);\n                return ({\n                    \"SelectScrollDownButton.useLayoutEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll2)\n                })[\"SelectScrollDownButton.useLayoutEffect\"];\n            }\n        }\n    }[\"SelectScrollDownButton.useLayoutEffect\"], [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollDown ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\nvar SelectScrollButtonImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n    const contentContext = useSelectContentContext(\"SelectScrollButton\", __scopeSelect);\n    const autoScrollTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const getItems = useCollection(__scopeSelect);\n    const clearAutoScrollTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"SelectScrollButtonImpl.useCallback[clearAutoScrollTimer]\": ()=>{\n            if (autoScrollTimerRef.current !== null) {\n                window.clearInterval(autoScrollTimerRef.current);\n                autoScrollTimerRef.current = null;\n            }\n        }\n    }[\"SelectScrollButtonImpl.useCallback[clearAutoScrollTimer]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SelectScrollButtonImpl.useEffect\": ()=>{\n            return ({\n                \"SelectScrollButtonImpl.useEffect\": ()=>clearAutoScrollTimer()\n            })[\"SelectScrollButtonImpl.useEffect\"];\n        }\n    }[\"SelectScrollButtonImpl.useEffect\"], [\n        clearAutoScrollTimer\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)({\n        \"SelectScrollButtonImpl.useLayoutEffect\": ()=>{\n            const activeItem = getItems().find({\n                \"SelectScrollButtonImpl.useLayoutEffect.activeItem\": (item)=>item.ref.current === document.activeElement\n            }[\"SelectScrollButtonImpl.useLayoutEffect.activeItem\"]);\n            activeItem?.ref.current?.scrollIntoView({\n                block: \"nearest\"\n            });\n        }\n    }[\"SelectScrollButtonImpl.useLayoutEffect\"], [\n        getItems\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...scrollIndicatorProps,\n        ref: forwardedRef,\n        style: {\n            flexShrink: 0,\n            ...scrollIndicatorProps.style\n        },\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerDown, ()=>{\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerMove, ()=>{\n            contentContext.onItemLeave?.();\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerLeave, ()=>{\n            clearAutoScrollTimer();\n        })\n    });\n});\nvar SEPARATOR_NAME = \"SelectSeparator\";\nvar SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nSelectSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"SelectArrow\";\nvar SelectArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === \"popper\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectArrow.displayName = ARROW_NAME;\nfunction shouldShowPlaceholder(value) {\n    return value === \"\" || value === void 0;\n}\nvar BubbleSelect = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, ...selectProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, ref);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"BubbleSelect.useEffect\": ()=>{\n            const select = ref.current;\n            const selectProto = window.HTMLSelectElement.prototype;\n            const descriptor = Object.getOwnPropertyDescriptor(selectProto, \"value\");\n            const setValue = descriptor.set;\n            if (prevValue !== value && setValue) {\n                const event = new Event(\"change\", {\n                    bubbles: true\n                });\n                setValue.call(select, value);\n                select.dispatchEvent(event);\n            }\n        }\n    }[\"BubbleSelect.useEffect\"], [\n        prevValue,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__.VisuallyHidden, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"select\", {\n            ...selectProps,\n            ref: composedRefs,\n            defaultValue: value\n        })\n    });\n});\nBubbleSelect.displayName = \"BubbleSelect\";\nfunction useTypeaheadSearch(onSearchChange) {\n    const handleSearchChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__.useCallbackRef)(onSearchChange);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const handleTypeaheadSearch = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useTypeaheadSearch.useCallback[handleTypeaheadSearch]\": (key)=>{\n            const search = searchRef.current + key;\n            handleSearchChange(search);\n            (function updateSearch(value) {\n                searchRef.current = value;\n                window.clearTimeout(timerRef.current);\n                if (value !== \"\") timerRef.current = window.setTimeout({\n                    \"useTypeaheadSearch.useCallback[handleTypeaheadSearch].updateSearch\": ()=>updateSearch(\"\")\n                }[\"useTypeaheadSearch.useCallback[handleTypeaheadSearch].updateSearch\"], 1e3);\n            })(search);\n        }\n    }[\"useTypeaheadSearch.useCallback[handleTypeaheadSearch]\"], [\n        handleSearchChange\n    ]);\n    const resetTypeahead = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useTypeaheadSearch.useCallback[resetTypeahead]\": ()=>{\n            searchRef.current = \"\";\n            window.clearTimeout(timerRef.current);\n        }\n    }[\"useTypeaheadSearch.useCallback[resetTypeahead]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useTypeaheadSearch.useEffect\": ()=>{\n            return ({\n                \"useTypeaheadSearch.useEffect\": ()=>window.clearTimeout(timerRef.current)\n            })[\"useTypeaheadSearch.useEffect\"];\n        }\n    }[\"useTypeaheadSearch.useEffect\"], []);\n    return [\n        searchRef,\n        handleTypeaheadSearch,\n        resetTypeahead\n    ];\n}\nfunction findNextItem(items, search, currentItem) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n    let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n    const excludeCurrentItem = normalizedSearch.length === 1;\n    if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v)=>v !== currentItem);\n    const nextItem = wrappedItems.find((item)=>item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextItem !== currentItem ? nextItem : void 0;\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root2 = Select;\nvar Trigger = SelectTrigger;\nvar Value = SelectValue;\nvar Icon = SelectIcon;\nvar Portal = SelectPortal;\nvar Content2 = SelectContent;\nvar Viewport = SelectViewport;\nvar Group = SelectGroup;\nvar Label = SelectLabel;\nvar Item = SelectItem;\nvar ItemText = SelectItemText;\nvar ItemIndicator = SelectItemIndicator;\nvar ScrollUpButton = SelectScrollUpButton;\nvar ScrollDownButton = SelectScrollDownButton;\nvar Separator = SelectSeparator;\nvar Arrow2 = SelectArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-select/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/slot/src/Slot.tsx\n\n\n\nvar Slot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n        return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      // @ts-ignore\n      ref: forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n};\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/useCallbackRef.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJEOlxcYmxvZy1nZW4tYWlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEByYWRpeC11aVxccmVhY3QtdXNlLWNhbGxiYWNrLXJlZlxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1jYWxsYmFjay1yZWYvc3JjL3VzZUNhbGxiYWNrUmVmLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VDYWxsYmFja1JlZihjYWxsYmFjaykge1xuICBjb25zdCBjYWxsYmFja1JlZiA9IFJlYWN0LnVzZVJlZihjYWxsYmFjayk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2FsbGJhY2tSZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gKC4uLmFyZ3MpID0+IGNhbGxiYWNrUmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlQ2FsbGJhY2tSZWZcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-controllable-state/src/useControllableState.tsx\n\n\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  }\n}) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onChange);\n  const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue;\n        const value2 = typeof nextValue === \"function\" ? setter(prop) : nextValue;\n        if (value2 !== prop) handleChange(value2);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const uncontrolledState = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n  const handleChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onChange);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n  return uncontrolledState;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jb250cm9sbGFibGUtc3RhdGUvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDK0I7QUFDbUM7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCx5RUFBeUUsdUJBQXVCO0FBQ2hHO0FBQ0E7QUFDQSx1QkFBdUIsZ0ZBQWM7QUFDckMsbUJBQW1CLDhDQUFpQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QsNEJBQTRCLDJDQUFjO0FBQzFDO0FBQ0EsdUJBQXVCLHlDQUFZO0FBQ25DLHVCQUF1QixnRkFBYztBQUNyQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJEOlxcYmxvZy1nZW4tYWlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEByYWRpeC11aVxccmVhY3QtdXNlLWNvbnRyb2xsYWJsZS1zdGF0ZVxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1jb250cm9sbGFibGUtc3RhdGUvc3JjL3VzZUNvbnRyb2xsYWJsZVN0YXRlLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VDYWxsYmFja1JlZiB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZlwiO1xuZnVuY3Rpb24gdXNlQ29udHJvbGxhYmxlU3RhdGUoe1xuICBwcm9wLFxuICBkZWZhdWx0UHJvcCxcbiAgb25DaGFuZ2UgPSAoKSA9PiB7XG4gIH1cbn0pIHtcbiAgY29uc3QgW3VuY29udHJvbGxlZFByb3AsIHNldFVuY29udHJvbGxlZFByb3BdID0gdXNlVW5jb250cm9sbGVkU3RhdGUoeyBkZWZhdWx0UHJvcCwgb25DaGFuZ2UgfSk7XG4gIGNvbnN0IGlzQ29udHJvbGxlZCA9IHByb3AgIT09IHZvaWQgMDtcbiAgY29uc3QgdmFsdWUgPSBpc0NvbnRyb2xsZWQgPyBwcm9wIDogdW5jb250cm9sbGVkUHJvcDtcbiAgY29uc3QgaGFuZGxlQ2hhbmdlID0gdXNlQ2FsbGJhY2tSZWYob25DaGFuZ2UpO1xuICBjb25zdCBzZXRWYWx1ZSA9IFJlYWN0LnVzZUNhbGxiYWNrKFxuICAgIChuZXh0VmFsdWUpID0+IHtcbiAgICAgIGlmIChpc0NvbnRyb2xsZWQpIHtcbiAgICAgICAgY29uc3Qgc2V0dGVyID0gbmV4dFZhbHVlO1xuICAgICAgICBjb25zdCB2YWx1ZTIgPSB0eXBlb2YgbmV4dFZhbHVlID09PSBcImZ1bmN0aW9uXCIgPyBzZXR0ZXIocHJvcCkgOiBuZXh0VmFsdWU7XG4gICAgICAgIGlmICh2YWx1ZTIgIT09IHByb3ApIGhhbmRsZUNoYW5nZSh2YWx1ZTIpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0VW5jb250cm9sbGVkUHJvcChuZXh0VmFsdWUpO1xuICAgICAgfVxuICAgIH0sXG4gICAgW2lzQ29udHJvbGxlZCwgcHJvcCwgc2V0VW5jb250cm9sbGVkUHJvcCwgaGFuZGxlQ2hhbmdlXVxuICApO1xuICByZXR1cm4gW3ZhbHVlLCBzZXRWYWx1ZV07XG59XG5mdW5jdGlvbiB1c2VVbmNvbnRyb2xsZWRTdGF0ZSh7XG4gIGRlZmF1bHRQcm9wLFxuICBvbkNoYW5nZVxufSkge1xuICBjb25zdCB1bmNvbnRyb2xsZWRTdGF0ZSA9IFJlYWN0LnVzZVN0YXRlKGRlZmF1bHRQcm9wKTtcbiAgY29uc3QgW3ZhbHVlXSA9IHVuY29udHJvbGxlZFN0YXRlO1xuICBjb25zdCBwcmV2VmFsdWVSZWYgPSBSZWFjdC51c2VSZWYodmFsdWUpO1xuICBjb25zdCBoYW5kbGVDaGFuZ2UgPSB1c2VDYWxsYmFja1JlZihvbkNoYW5nZSk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHByZXZWYWx1ZVJlZi5jdXJyZW50ICE9PSB2YWx1ZSkge1xuICAgICAgaGFuZGxlQ2hhbmdlKHZhbHVlKTtcbiAgICAgIHByZXZWYWx1ZVJlZi5jdXJyZW50ID0gdmFsdWU7XG4gICAgfVxuICB9LCBbdmFsdWUsIHByZXZWYWx1ZVJlZiwgaGFuZGxlQ2hhbmdlXSk7XG4gIHJldHVybiB1bmNvbnRyb2xsZWRTdGF0ZTtcbn1cbmV4cG9ydCB7XG4gIHVzZUNvbnRyb2xsYWJsZVN0YXRlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/useEscapeKeydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lc2NhcGUta2V5ZG93bi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUNtQztBQUNsRTtBQUNBLDBCQUEwQixnRkFBYztBQUN4QyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0QsZUFBZTtBQUM5RSwrRUFBK0UsZUFBZTtBQUM5RixHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJEOlxcYmxvZy1nZW4tYWlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEByYWRpeC11aVxccmVhY3QtdXNlLWVzY2FwZS1rZXlkb3duXFxkaXN0XFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWVzY2FwZS1rZXlkb3duL3NyYy91c2VFc2NhcGVLZXlkb3duLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VDYWxsYmFja1JlZiB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZlwiO1xuZnVuY3Rpb24gdXNlRXNjYXBlS2V5ZG93bihvbkVzY2FwZUtleURvd25Qcm9wLCBvd25lckRvY3VtZW50ID0gZ2xvYmFsVGhpcz8uZG9jdW1lbnQpIHtcbiAgY29uc3Qgb25Fc2NhcGVLZXlEb3duID0gdXNlQ2FsbGJhY2tSZWYob25Fc2NhcGVLZXlEb3duUHJvcCk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlS2V5RG93biA9IChldmVudCkgPT4ge1xuICAgICAgaWYgKGV2ZW50LmtleSA9PT0gXCJFc2NhcGVcIikge1xuICAgICAgICBvbkVzY2FwZUtleURvd24oZXZlbnQpO1xuICAgICAgfVxuICAgIH07XG4gICAgb3duZXJEb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKFwia2V5ZG93blwiLCBoYW5kbGVLZXlEb3duLCB7IGNhcHR1cmU6IHRydWUgfSk7XG4gICAgcmV0dXJuICgpID0+IG93bmVyRG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgaGFuZGxlS2V5RG93biwgeyBjYXB0dXJlOiB0cnVlIH0pO1xuICB9LCBbb25Fc2NhcGVLZXlEb3duLCBvd25lckRvY3VtZW50XSk7XG59XG5leHBvcnQge1xuICB1c2VFc2NhcGVLZXlkb3duXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/useLayoutEffect.tsx\n\nvar useLayoutEffect2 = Boolean(globalThis?.document) ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsdURBQXVELGtEQUFxQjtBQUM1RTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxibG9nLWdlbi1haVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQHJhZGl4LXVpXFxyZWFjdC11c2UtbGF5b3V0LWVmZmVjdFxcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1sYXlvdXQtZWZmZWN0L3NyYy91c2VMYXlvdXRFZmZlY3QudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VMYXlvdXRFZmZlY3QyID0gQm9vbGVhbihnbG9iYWxUaGlzPy5kb2N1bWVudCkgPyBSZWFjdC51c2VMYXlvdXRFZmZlY3QgOiAoKSA9PiB7XG59O1xuZXhwb3J0IHtcbiAgdXNlTGF5b3V0RWZmZWN0MiBhcyB1c2VMYXlvdXRFZmZlY3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-previous/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-previous/src/usePrevious.tsx\n\nfunction usePrevious(value) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef({ value, previous: value });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1wcmV2aW91cy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQytCO0FBQy9CO0FBQ0EsY0FBYyx5Q0FBWSxHQUFHLHdCQUF3QjtBQUNyRCxTQUFTLDBDQUFhO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJEOlxcYmxvZy1nZW4tYWlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEByYWRpeC11aVxccmVhY3QtdXNlLXByZXZpb3VzXFxkaXN0XFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLXByZXZpb3VzL3NyYy91c2VQcmV2aW91cy50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlUHJldmlvdXModmFsdWUpIHtcbiAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmKHsgdmFsdWUsIHByZXZpb3VzOiB2YWx1ZSB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4ge1xuICAgIGlmIChyZWYuY3VycmVudC52YWx1ZSAhPT0gdmFsdWUpIHtcbiAgICAgIHJlZi5jdXJyZW50LnByZXZpb3VzID0gcmVmLmN1cnJlbnQudmFsdWU7XG4gICAgICByZWYuY3VycmVudC52YWx1ZSA9IHZhbHVlO1xuICAgIH1cbiAgICByZXR1cm4gcmVmLmN1cnJlbnQucHJldmlvdXM7XG4gIH0sIFt2YWx1ZV0pO1xufVxuZXhwb3J0IHtcbiAgdXNlUHJldmlvdXNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-size/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSize: () => (/* binding */ useSize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/use-size/src/useSize.tsx\n\n\nfunction useSize(element) {\n  const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState(void 0);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (element) {\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({ width, height });\n      });\n      resizeObserver.observe(element, { box: \"border-box\" });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-size/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/visually-hidden/src/VisuallyHidden.tsx\n\n\n\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: {\n          // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n          position: \"absolute\",\n          border: 0,\n          width: 1,\n          height: 1,\n          padding: 0,\n          margin: -1,\n          overflow: \"hidden\",\n          clip: \"rect(0, 0, 0, 0)\",\n          whiteSpace: \"nowrap\",\n          wordWrap: \"normal\",\n          ...props.style\n        }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ })

};
;