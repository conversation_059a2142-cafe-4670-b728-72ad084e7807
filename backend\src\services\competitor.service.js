const axios = require('axios');
const { model } = require('../config/gemini.config');

class CompetitorService {
  constructor() {
    // Add your competitor list here or fetch from sheets
    this.competitors = [
      'sunrun.com',
      'tesla.com/solar',
      'enphase.com',
      'solaredge.com',
      'aurorasolar.com'
    ];
  }

  async analyzeCompetitors(focusKeyword) {
    try {
      // This would normally use a SERP API or web scraping
      // For now, we'll use <PERSON> to simulate the analysis
      const prompt = `
        Analyze competitor content for keyword: "${focusKeyword}"
        Top solar industry competitors: ${this.competitors.join(', ')}
        
        Provide analysis in this JSON format:
        {
          "topRankingPages": [
            {
              "competitor": "company name",
              "title": "page title",
              "wordCount": "estimated word count",
              "keyPoints": ["point 1", "point 2"],
              "contentGaps": ["gap 1", "gap 2"]
            }
          ],
          "commonH2Topics": ["topic 1", "topic 2"],
          "averageWordCount": "number",
          "contentOpportunities": ["opportunity 1", "opportunity 2"]
        }
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      try {
        const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } catch (parseError) {
        console.error('Competitor analysis parsing error:', parseError);
      }

      // Fallback analysis
      return {
        topRankingPages: [
          {
            competitor: "Industry Leader",
            title: `Complete Guide to ${focusKeyword}`,
            wordCount: "2500",
            keyPoints: ["Comprehensive coverage", "Expert insights"],
            contentGaps: ["Local market specifics", "2025 updates"]
          }
        ],
        commonH2Topics: [
          `What is ${focusKeyword}`,
          `Benefits of ${focusKeyword}`,
          "Step-by-Step Guide",
          "Common Mistakes",
          "Cost Analysis"
        ],
        averageWordCount: "2000",
        contentOpportunities: [
          "More detailed implementation guide",
          "Local market analysis",
          "ROI calculator integration"
        ]
      };
    } catch (error) {
      console.error('Error analyzing competitors:', error);
      throw error;
    }
  }

  async generateKeywordCluster(focusKeyword, competitorAnalysis) {
    try {
      const prompt = `
        Generate a keyword cluster for main keyword: "${focusKeyword}"
        Based on competitor topics: ${competitorAnalysis.commonH2Topics.join(', ')}
        
        Create a comprehensive keyword cluster with search volume estimates.
        Include LSI keywords and related terms.
        
        Return in this JSON format:
        {
          "primaryKeyword": "${focusKeyword}",
          "secondaryKeywords": [
            {"keyword": "term", "searchVolume": "estimate", "difficulty": "low/medium/high"}
          ],
          "lsiKeywords": ["term1", "term2"],
          "h2Keywords": [
            {"h2Title": "title", "targetKeyword": "keyword", "searchIntent": "informational/transactional"}
          ]
        }
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      try {
        const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } catch (parseError) {
        console.error('Keyword cluster parsing error:', parseError);
      }

      // Fallback cluster
      return {
        primaryKeyword: focusKeyword,
        secondaryKeywords: [
          {keyword: `${focusKeyword} guide`, searchVolume: "1000", difficulty: "medium"},
          {keyword: `${focusKeyword} cost`, searchVolume: "800", difficulty: "low"},
          {keyword: `best ${focusKeyword}`, searchVolume: "600", difficulty: "high"}
        ],
        lsiKeywords: ["solar", "installation", "permits", "efficiency", "ROI"],
        h2Keywords: competitorAnalysis.commonH2Topics.map(topic => ({
          h2Title: topic,
          targetKeyword: topic.toLowerCase().replace(/[^a-z0-9\s]/g, ''),
          searchIntent: "informational"
        }))
      };
    } catch (error) {
      console.error('Error generating keyword cluster:', error);
      throw error;
    }
  }

  async analyzeTrends(focusKeyword) {
    try {
      const currentYear = new Date().getFullYear();
      const prompt = `
        Analyze current trends for: "${focusKeyword}" in ${currentYear}
        Focus on solar industry trends, regulations, and market changes.
        
        Return in JSON format:
        {
          "currentTrends": ["trend 1", "trend 2"],
          "emergingTopics": ["topic 1", "topic 2"],
          "seasonalFactors": "description",
          "regulatoryUpdates": ["update 1", "update 2"],
          "marketInsights": ["insight 1", "insight 2"]
        }
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      try {
        const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } catch (parseError) {
        console.error('Trends analysis parsing error:', parseError);
      }

      return {
        currentTrends: [
          "Increased focus on energy independence",
          "Growing adoption of battery storage"
        ],
        emergingTopics: [
          "Virtual power plants",
          "AI-powered energy management"
        ],
        seasonalFactors: "Peak installation season approaching",
        regulatoryUpdates: [
          "New tax incentives available",
          "Updated interconnection standards"
        ],
        marketInsights: [
          "Supply chain improvements",
          "Cost reductions in panel technology"
        ]
      };
    } catch (error) {
      console.error('Error analyzing trends:', error);
      throw error;
    }
  }
}

module.exports = new CompetitorService();