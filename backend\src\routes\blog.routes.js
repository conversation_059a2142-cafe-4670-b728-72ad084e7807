const router = require('express').Router();
const blogController = require('../controllers/blog.controller');

// Blog generation workflow
router.post('/start', blogController.startBlog);
router.post('/select-keyword-analyze', blogController.selectKeywordAndAnalyze);
router.post('/generate-meta-scores', blogController.generateMetaWithScores);
router.post('/select-meta', blogController.selectMeta);
router.post('/generate-structured-content', blogController.generateStructuredContent);
router.post('/regenerate-block', blogController.regenerateBlock);
router.post('/generate-links', blogController.generateLinks);
router.post('/deploy-wordpress', blogController.deployToWordPress);

// Draft management
router.get('/draft/:draftId', blogController.getDraft);
router.get('/drafts', blogController.listDrafts);
router.post('/save-draft', blogController.saveDraft);

// WordPress
router.get('/test-wordpress', blogController.testWordPressConnection);

module.exports = router;