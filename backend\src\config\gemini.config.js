const { GoogleGenerativeAI } = require('@google/generative-ai');

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Try multiple model options
let model;
try {
  model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
} catch (error) {
  try {
    model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
  } catch (error2) {
    model = genAI.getGenerativeModel({ model: "gemini-pro" });
  }
}

module.exports = { model };