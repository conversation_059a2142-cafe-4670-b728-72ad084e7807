const axios = require('axios');

class WordPressService {
  constructor() {
    this.baseUrl = process.env.WORDPRESS_URL;
    this.username = process.env.WORDPRESS_USERNAME;
    this.password = process.env.WORDPRESS_PASSWORD;
    this.authToken = Buffer.from(`${this.username}:${this.password}`).toString('base64');
  }

  async createPost(postData) {
    try {
      const wpPostData = {
        title: postData.title,
        content: postData.content,
        excerpt: postData.excerpt,
        status: postData.status || 'draft',
        format: 'standard',
        categories: [], // Add category IDs if needed
        tags: [], // Add tag IDs if needed
        meta: postData.meta || {}
      };

      const response = await axios.post(
        `${this.baseUrl}/wp-json/wp/v2/posts`,
        wpPostData,
        {
          headers: {
            'Authorization': `Basic ${this.authToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        postId: response.data.id,
        postUrl: response.data.link,
        editUrl: `${this.baseUrl}/wp-admin/post.php?post=${response.data.id}&action=edit`
      };
    } catch (error) {
      console.error('WordPress API Error:', error.response?.data || error.message);
      throw new Error(`Failed to create WordPress post: ${error.message}`);
    }
  }

  async testConnection() {
    try {
      const response = await axios.get(
        `${this.baseUrl}/wp-json/wp/v2/users/me`,
        {
          headers: {
            'Authorization': `Basic ${this.authToken}`
          }
        }
      );
      
      return {
        connected: true,
        user: response.data.name,
        userId: response.data.id
      };
    } catch (error) {
      return {
        connected: false,
        error: error.message
      };
    }
  }
}

module.exports = new WordPressService();