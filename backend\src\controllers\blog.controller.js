const googleSheetsService = require('../services/googleSheets.service');
const geminiService = require('../services/gemini.service');
const draftService = require('../services/draft.service');
const competitorService = require('../services/competitor.service');
const citationService = require('../services/citation.service');
const wordpressService = require('../services/wordpress.service');

class BlogController {
  async startBlog(req, res) {
    try {
      const { companyName } = req.body;
      
      // Step 1: Get company data
      const companyData = await googleSheetsService.getCompanyByName(companyName);
      if (!companyData) {
        return res.status(404).json({ error: 'Company not found' });
      }
      
      // Step 2: Get blog keywords from sheet
      const blogKeywords = await googleSheetsService.getManualKeywords();
      
      // Create initial draft
      const draft = await draftService.createDraft({
        companyId: companyData.id,
        companyName: companyData.companyName,
        companyData,
        availableKeywords: blogKeywords,
        status: 'keyword_selection'
      });
      
      res.json({
        draftId: draft.id,
        company: companyData,
        availableKeywords: blogKeywords
      });
    } catch (error) {
      console.error('Error starting blog:', error);
      res.status(500).json({ error: error.message });
    }
  }

  async selectKeywordAndAnalyze(req, res) {
    try {
      const { draftId, selectedKeyword } = req.body;
      
      const draft = await draftService.getDraft(draftId);
      
      // Step 3: Fetch aligned data for selected keyword
      const alignedData = await googleSheetsService.getAlignedBlogData(selectedKeyword);
      
      // Step 4: Competitor Analysis
      const competitorAnalysis = await competitorService.analyzeCompetitors(selectedKeyword);
      
      // Step 5: Generate Keyword Cluster
      const keywordCluster = await competitorService.generateKeywordCluster(
        selectedKeyword, 
        competitorAnalysis
      );
      
      // Step 6: Analyze Trends
      const trendsAnalysis = await competitorService.analyzeTrends(selectedKeyword);
      
      // Update draft with analysis
      await draftService.updateDraft(draftId, {
        selectedKeyword,
        alignedData,
        competitorAnalysis,
        keywordCluster,
        trendsAnalysis,
        status: 'meta_generation'
      });
      
      res.json({
        message: 'Keyword selected and analyzed',
        selectedKeyword,
        alignedData,
        competitorAnalysis,
        keywordCluster,
        trendsAnalysis
      });
    } catch (error) {
      console.error('Error in keyword analysis:', error);
      res.status(500).json({ error: error.message });
    }
  }

  async generateMetaWithScores(req, res) {
    try {
      const { draftId } = req.body;
      
      const draft = await draftService.getDraft(draftId);
      
      // Generate 3 H1 + Meta options with keyword cluster scores
      const metaOptions = await this.generateScoredMetaOptions(
        draft.selectedKeyword,
        draft.keywordCluster,
        draft.companyData,
        draft.trendsAnalysis
      );
      
      await draftService.updateDraft(draftId, {
        metaSuggestions: metaOptions,
        status: 'meta_selection'
      });
      
      res.json({ metaOptions });
    } catch (error) {
      console.error('Error generating meta:', error);
      res.status(500).json({ error: error.message });
    }
  }

  async generateScoredMetaOptions(keyword, keywordCluster, companyData, trends) {
    const { model } = require('../config/gemini.config');
    const currentYear = new Date().getFullYear();
    
    const prompt = `
      Generate 3 SEO-optimized H1 + Meta Title + Meta Description options for:
      Primary Keyword: ${keyword}
      Secondary Keywords: ${keywordCluster.secondaryKeywords.map(k => k.keyword).join(', ')}
      LSI Keywords: ${keywordCluster.lsiKeywords.join(', ')}
      Company: ${companyData.companyName}
      Current Trends: ${trends.currentTrends.join(', ')}
      Year: ${currentYear}
      
      For each option, calculate SEO scores based on:
      - Keyword inclusion (40 points)
      - Length optimization (20 points)
      - Readability (20 points)
      - Trend relevance (20 points)
      
      Return in JSON format:
      {
        "options": [
          {
            "h1Title": "title here",
            "metaTitle": "meta title here",
            "metaDescription": "description here",
            "scores": {
              "keywordScore": 0-40,
              "lengthScore": 0-20,
              "readabilityScore": 0-20,
              "trendScore": 0-20,
              "totalScore": 0-100
            },
            "keywordsIncluded": ["keyword1", "keyword2"]
          }
        ]
      }
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    try {
      const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return parsed.options;
      }
        } catch (parseError) {
      console.error('Meta options parsing error:', parseError);
    }

    // Fallback meta options with scores
    return [
      {
        h1Title: `${keyword} - Complete Guide ${currentYear}`,
        metaTitle: `${keyword} Guide ${currentYear} | ${companyData.companyName}`,
        metaDescription: `Master ${keyword} with our expert guide. Learn best practices, cost analysis, and implementation strategies from ${companyData.companyName} professionals.`,
        scores: {
          keywordScore: 35,
          lengthScore: 18,
          readabilityScore: 17,
          trendScore: 15,
          totalScore: 85
        },
        keywordsIncluded: [keyword, "guide", currentYear.toString()]
      },
      {
        h1Title: `Professional ${keyword} Services & Solutions`,
        metaTitle: `${keyword} Services | Expert Solar Solutions`,
        metaDescription: `Professional ${keyword} services by ${companyData.companyName}. Trusted by installers nationwide for reliable, efficient solar solutions and expert support.`,
        scores: {
          keywordScore: 38,
          lengthScore: 19,
          readabilityScore: 18,
          trendScore: 17,
          totalScore: 92
        },
        keywordsIncluded: [keyword, "services", "solutions", "professional"]
      },
      {
        h1Title: `${keyword}: Essential Tips & Best Practices`,
        metaTitle: `${keyword} Tips & Best Practices ${currentYear}`,
        metaDescription: `Discover essential ${keyword} tips and industry best practices. ${companyData.companyName} shares proven strategies for successful solar projects in ${currentYear}.`,
        scores: {
          keywordScore: 36,
          lengthScore: 17,
          readabilityScore: 19,
          trendScore: 16,
          totalScore: 88
        },
        keywordsIncluded: [keyword, "tips", "best practices", currentYear.toString()]
      }
    ];
  }

  async generateStructuredContent(req, res) {
    try {
      const { draftId } = req.body;
      
      const draft = await draftService.getDraft(draftId);
      
      if (!draft.finalMeta) {
        return res.status(400).json({ error: 'Please select meta tags first' });
      }
      
      // Generate content with exact structure: H2s + subcontent + images
      const structuredContent = await this.generateWordPressFormattedContent(
        draft.finalMeta,
        draft.selectedKeyword,
        draft.keywordCluster,
        draft.companyData,
        draft.competitorAnalysis,
        draft.trendsAnalysis
      );
      
      // Generate citations
      const citations = await citationService.generateCitations(
        draft.selectedKeyword,
        structuredContent.sections.map(s => s.h2).join(', ')
      );
      
      // Structure as blocks for editing
      const blogBlocks = this.structureContentAsBlocks(structuredContent, citations);
      
      await draftService.updateDraft(draftId, {
        blogBlocks,
        structuredContent,
        citations: citations.citations,
        status: 'content_review'
      });
      
      res.json({
        blogBlocks,
        totalBlocks: blogBlocks.length,
        structure: {
          sections: structuredContent.sections.length,
          images: 3, // 1 feature + 2 in-blog
          citations: citations.citations.length
        }
      });
    } catch (error) {
      console.error('Error generating structured content:', error);
      res.status(500).json({ error: error.message });
    }
  }

  async generateWordPressFormattedContent(meta, keyword, keywordCluster, companyData, competitorAnalysis, trends) {
    const { model } = require('../config/gemini.config');
    const currentYear = new Date().getFullYear();
    
    const prompt = `
      Create a comprehensive blog post with this EXACT structure:
      
      Title: ${meta.h1Title}
      Primary Keyword: ${keyword}
      Word Count: 2000-2500 words
      Company: ${companyData.companyName}
      Year: ${currentYear}
      
      Competitor H2 topics to cover: ${competitorAnalysis.commonH2Topics.join(', ')}
      Trends to incorporate: ${trends.currentTrends.join(', ')}
      Keywords to naturally include: ${keywordCluster.lsiKeywords.join(', ')}
      
      REQUIRED STRUCTURE:
      1. Introduction (150-200 words) - Hook, problem statement, what reader will learn
      2. Feature Image - Description for hero image showcasing the main topic
      3. 5-6 H2 Sections, each with:
         - H2 heading (keyword-optimized)
         - 3-4 paragraphs (250-400 words total)
         - Include statistics and data points
      4. In-blog Image 1 - After 2nd H2 section
      5. In-blog Image 2 - After 4th H2 section
      6. Conclusion (150-200 words) - Summary, CTA for ${companyData.companyName}
      
      Return in this EXACT JSON format:
      {
        "introduction": "Full introduction text...",
        "featureImage": {
          "alt": "Descriptive alt text",
          "title": "Image title",
          "description": "What the image should show",
          "placement": "after_introduction"
        },
        "sections": [
          {
            "h2": "H2 Heading Text",
            "content": "Full content for this section...",
            "targetKeyword": "specific keyword for this section"
          }
        ],
        "inBlogImages": [
          {
            "alt": "Alt text for image 1",
            "title": "Image 1 title",
            "description": "What image 1 should show",
            "placement": "after_section_2"
          },
          {
            "alt": "Alt text for image 2",
            "title": "Image 2 title",
            "description": "What image 2 should show",
            "placement": "after_section_4"
          }
        ],
        "conclusion": "Full conclusion text..."
      }
    `;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    try {
      const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (parseError) {
      console.error('Content parsing error:', parseError);
    }

    // Fallback structured content
    return this.generateFallbackContent(meta, keyword, companyData, currentYear);
  }

  structureContentAsBlocks(content, citations) {
    const blocks = [];
    let blockIndex = 0;
    
    // Introduction block
    blocks.push({
      id: `block_${blockIndex++}`,
      type: 'introduction',
      content: content.introduction,
      editable: true,
      wordCount: content.introduction.split(' ').length
    });
    
    // Feature image block
    blocks.push({
      id: `block_${blockIndex++}`,
      type: 'image',
      imageType: 'feature',
      ...content.featureImage,
      editable: true
    });
    
    // Process sections with images
    content.sections.forEach((section, sectionIndex) => {
      // Section block
      blocks.push({
        id: `block_${blockIndex++}`,
        type: 'section',
        h2: section.h2,
        content: section.content,
        targetKeyword: section.targetKeyword,
        editable: true,
        sectionNumber: sectionIndex + 1,
        wordCount: section.content.split(' ').length
      });
      
      // Add in-blog images at specified positions
      if (sectionIndex === 1) { // After 2nd section
        blocks.push({
          id: `block_${blockIndex++}`,
          type: 'image',
          imageType: 'in-blog',
          ...content.inBlogImages[0],
          editable: true
        });
      } else if (sectionIndex === 3) { // After 4th section
        blocks.push({
          id: `block_${blockIndex++}`,
          type: 'image',
          imageType: 'in-blog',
          ...content.inBlogImages[1],
          editable: true
        });
      }
    });
    
    // Conclusion block
    blocks.push({
      id: `block_${blockIndex++}`,
      type: 'conclusion',
      content: content.conclusion,
      editable: true,
      wordCount: content.conclusion.split(' ').length
    });
    
    // References block
    blocks.push({
      id: `block_${blockIndex++}`,
      type: 'references',
      content: citationService.generateReferenceSection(citations.citations),
      citations: citations.citations,
      editable: false
    });
    
    return blocks;
  }

  async regenerateBlock(req, res) {
    try {
      const { draftId, blockId, regenerationType, customPrompt } = req.body;
      
      const draft = await draftService.getDraft(draftId);
            const block = draft.blogBlocks.find(b => b.id === blockId);
      
      if (!block) {
        return res.status(404).json({ error: 'Block not found' });
      }
      
      let newContent;
      
      if (regenerationType === 'ai') {
        // AI regeneration based on block type
        newContent = await this.regenerateBlockContent(
          block,
          draft,
          customPrompt
        );
      } else if (regenerationType === 'manual') {
        // Manual edit - content provided in request
        newContent = req.body.newContent;
      }
      
      // Update the block
      const updatedBlocks = draft.blogBlocks.map(b => 
        b.id === blockId ? { ...b, content: newContent, lastEdited: new Date().toISOString() } : b
      );
      
      await draftService.updateDraft(draftId, {
        blogBlocks: updatedBlocks
      });
      
      res.json({
        success: true,
        updatedBlock: { ...block, content: newContent }
      });
    } catch (error) {
      console.error('Error regenerating block:', error);
      res.status(500).json({ error: error.message });
    }
  }

  async regenerateBlockContent(block, draft, customPrompt) {
    const { model } = require('../config/gemini.config');
    const currentYear = new Date().getFullYear();
    
    let prompt;
    
    switch (block.type) {
      case 'introduction':
        prompt = customPrompt || `Rewrite the introduction for "${draft.finalMeta.h1Title}" about ${draft.selectedKeyword}. Make it engaging, ${block.wordCount || 150}-200 words, include the keyword naturally. Target: ${draft.alignedData.targetAudience}. Year: ${currentYear}`;
        break;
        
      case 'section':
        prompt = customPrompt || `Rewrite this section with H2: "${block.h2}". Topic: ${draft.selectedKeyword}. Target keyword: ${block.targetKeyword}. Make it informative, ${block.wordCount || 300}-400 words. Include data/statistics. Year: ${currentYear}`;
        break;
        
      case 'conclusion':
        prompt = customPrompt || `Rewrite the conclusion for "${draft.finalMeta.h1Title}". Summarize key points, include strong CTA for ${draft.companyData.companyName}. ${block.wordCount || 150}-200 words. Year: ${currentYear}`;
        break;
        
      case 'image':
        prompt = customPrompt || `Generate a new description for a ${block.imageType} image in a blog about ${draft.selectedKeyword}. Make it specific, relevant to solar industry, and SEO-friendly.`;
        const imageResult = await model.generateContent(prompt);
        return {
          ...block,
          description: imageResult.response.text(),
          alt: `${draft.selectedKeyword} - ${block.imageType} image`,
          regeneratedAt: new Date().toISOString()
        };
        
      default:
        prompt = customPrompt || `Improve this content about ${draft.selectedKeyword}`;
    }
    
    const result = await model.generateContent(prompt);
    return result.response.text();
  }

  async generateLinks(req, res) {
    try {
      const { draftId } = req.body;
      
      const draft = await draftService.getDraft(draftId);
      
      // Generate inbound and outbound links
      const links = await this.generateInboundOutboundLinks(
        draft.selectedKeyword,
        draft.blogBlocks,
        draft.companyData
      );
      
      await draftService.updateDraft(draftId, {
        links,
        status: 'links_generated'
      });
      
      res.json({ links });
    } catch (error) {
      console.error('Error generating links:', error);
      res.status(500).json({ error: error.message });
    }
  }

  async generateInboundOutboundLinks(keyword, blocks, companyData) {
    const { model } = require('../config/gemini.config');
    
    // Extract all H2 topics
    const h2Topics = blocks
      .filter(b => b.type === 'section')
      .map(b => b.h2);
    
    const prompt = `
      Generate internal and external link suggestions for a blog about "${keyword}".
      Company: ${companyData.companyName}
      H2 Topics: ${h2Topics.join(', ')}
      
      Provide:
      1. 3-4 Internal links (to other potential ${companyData.companyName} pages)
      2. 4-5 External links (to authoritative non-competitor sites)
      
      Return in JSON format:
      {
        "internalLinks": [
          {
            "anchorText": "text to link",
            "suggestedUrl": "/suggested-page-url",
            "context": "where in the content this fits",
            "relevance": "why this link adds value"
          }
        ],
        "externalLinks": [
          {
            "anchorText": "text to link",
            "domain": "authoritative domain",
            "context": "where in the content this fits",
            "relevance": "why this link adds value"
          }
        ]
      }
    `;
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    try {
      const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (parseError) {
      console.error('Links parsing error:', parseError);
    }
    
    // Fallback links
    return {
      internalLinks: [
        {
          anchorText: "solar installation services",
          suggestedUrl: "/services/solar-installation",
          context: "In introduction or services section",
          relevance: "Links to main service page"
        },
        {
          anchorText: "get a quote",
          suggestedUrl: "/quote",
          context: "In conclusion CTA",
          relevance: "Conversion opportunity"
        }
      ],
      externalLinks: [
        {
          anchorText: "Department of Energy",
          domain: "energy.gov",
          context: "When mentioning incentives",
          relevance: "Authority on solar incentives"
        },
        {
          anchorText: "solar industry statistics",
          domain: "seia.org",
          context: "When citing industry data",
          relevance: "Industry authority"
        }
      ]
    };
  }

  async deployToWordPress(req, res) {
    try {
      const { draftId } = req.body;
      
      const draft = await draftService.getDraft(draftId);
      
      if (!draft.blogBlocks || draft.status !== 'links_generated') {
        return res.status(400).json({ 
          error: 'Blog must be fully generated with links before publishing' 
        });
      }
      
      // Convert blocks to WordPress-formatted HTML
      const wpContent = this.convertToWordPressHTML(draft);
      
      // Deploy to WordPress
      const result = await wordpressService.createPost({
        title: draft.finalMeta.h1Title,
        content: wpContent,
        excerpt: draft.finalMeta.metaDescription,
        meta: {
          _yoast_wpseo_title: draft.finalMeta.metaTitle,
          _yoast_wpseo_metadesc: draft.finalMeta.metaDescription,
          _yoast_wpseo_focuskw: draft.selectedKeyword
        },
        status: 'draft' // Always save as draft for final review
      });
      
      await draftService.updateDraft(draftId, {
        status: 'deployed_to_wordpress',
        wordpressPostId: result.postId,
        wordpressUrl: result.postUrl,
        wordpressEditUrl: result.editUrl,
        deployedAt: new Date().toISOString()
      });
      
      res.json({
        message: 'Successfully deployed to WordPress',
        ...result
      });
    } catch (error) {
      console.error('Error deploying to WordPress:', error);
      res.status(500).json({ error: error.message });
    }
  }
  async selectMeta(req, res) {
  try {
    const { draftId, selectedMetaIndex } = req.body;
    
    const draft = await draftService.getDraft(draftId);
    const selectedMeta = draft.metaSuggestions[selectedMetaIndex];
    
    await draftService.updateDraft(draftId, {
      finalMeta: selectedMeta,
      status: 'ready_for_content'
    });
    
    res.json({
      message: 'Meta tags selected successfully',
      selectedMeta
    });
  } catch (error) {
    console.error('Error selecting meta:', error);
    res.status(500).json({ error: error.message });
  }
}

  convertToWordPressHTML(draft) {
    let html = '';
    
    draft.blogBlocks.forEach((block, index) => {
      switch (block.type) {
        case 'introduction':
          html += `<p>${block.content}</p>\n\n`;
          break;
          
        case 'image':
          if (block.imageType === 'feature') {
            html += `<!-- wp:image {"align":"wide"} -->\n`;
            html += `<figure class="wp-block-image alignwide"><img src="[PLACEHOLDER_${block.imageType}_IMAGE]" alt="${block.alt}" title="${block.title}"/><figcaption>${block.title}</figcaption></figure>\n`;
            html += `<!-- /wp:image -->\n\n`;
          } else {
            html += `<!-- wp:image -->\n`;
            html += `<figure class="wp-block-image"><img src="[PLACEHOLDER_${block.imageType}_IMAGE_${index}]" alt="${block.alt}" title="${block.title}"/><figcaption>${block.title}</figcaption></figure>\n`;
            html += `<!-- /wp:image -->\n\n`;
          }
          break;
          
        case 'section':
          html += `<!-- wp:heading -->\n`;
          html += `<h2>${block.h2}</h2>\n`;
          html += `<!-- /wp:heading -->\n\n`;
          html += `<!-- wp:paragraph -->\n`;
          html += `<p>${block.content}</p>\n`;
          html += `<!-- /wp:paragraph -->\n\n`;
          break;
          
        case 'conclusion':
          html += `<!-- wp:paragraph -->\n`;
          html += `<p><strong>${block.content}</strong></p>\n`;
          html += `<!-- /wp:paragraph -->\n\n`;
          break;
          
        case 'references':
          html += block.content + '\n\n';
          break;
      }
    });
    
    // Add schema markup for SEO
    html += this.generateSchemaMarkup(draft);
    
    return html;
  }

  generateSchemaMarkup(draft) {
    const schema = {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": draft.finalMeta.h1Title,
      "description": draft.finalMeta.metaDescription,
      "author": {
        "@type": "Organization",
        "name": draft.companyData.companyName
      },
      "datePublished": new Date().toISOString(),
      "dateModified": new Date().toISOString(),
      "publisher": {
        "@type": "Organization",
        "name": draft.companyData.companyName
      }
    };
    
    return `\n<!-- Schema Markup -->\n<script type="application/ld+json">\n${JSON.stringify(schema, null, 2)}\n</script>`;
  }

  generateFallbackContent(meta, keyword, companyData, year) {
    return {
      introduction: `Welcome to our comprehensive guide on ${keyword}. As the solar industry continues to evolve in ${year}, understanding ${keyword} has become crucial for professionals and businesses alike. In this guide, ${companyData.companyName} shares expert insights and proven strategies to help you succeed.`,
      featureImage: {
        alt: `${keyword} comprehensive guide`,
                title: `Professional ${keyword} Guide`,
        description: `Hero image showcasing modern solar installation with ${keyword} elements highlighted`,
        placement: "after_introduction"
      },
      sections: [
        {
          h2: `What is ${keyword}?`,
          content: `Understanding ${keyword} is fundamental to success in the solar industry. This comprehensive overview covers the essential concepts, terminology, and applications that every professional should know. We'll explore how ${keyword} impacts project efficiency, cost-effectiveness, and overall success rates in solar installations.`,
          targetKeyword: keyword
        },
        {
          h2: `Benefits of Professional ${keyword} Services`,
          content: `Implementing proper ${keyword} strategies offers numerous advantages for solar projects. From improved efficiency and reduced costs to enhanced safety and compliance, the benefits are substantial. ${companyData.companyName} has helped countless professionals achieve these benefits through our specialized services and expertise.`,
          targetKeyword: `${keyword} benefits`
        },
        {
          h2: `Step-by-Step ${keyword} Implementation Guide`,
          content: `Success with ${keyword} requires a systematic approach. This detailed guide walks you through each phase of implementation, from initial planning to final execution. Follow these proven steps to ensure optimal results and avoid common pitfalls that can derail projects.`,
          targetKeyword: `${keyword} guide`
        },
        {
          h2: `Common ${keyword} Challenges and Solutions`,
          content: `Every solar project faces unique challenges related to ${keyword}. Understanding these potential issues and their solutions prepares you for success. Learn from real-world examples and expert insights to navigate obstacles effectively and maintain project momentum.`,
          targetKeyword: `${keyword} challenges`
        },
        {
          h2: `${keyword} Best Practices for ${year}`,
          content: `Stay ahead of the curve with the latest ${keyword} best practices for ${year}. These industry-leading strategies reflect recent technological advances, regulatory changes, and market trends. Implementing these practices ensures your projects meet current standards and exceed client expectations.`,
          targetKeyword: `${keyword} best practices`
        },
        {
          h2: `Future of ${keyword} in Solar Industry`,
          content: `The solar industry continues to evolve rapidly, and ${keyword} plays a crucial role in this transformation. Explore emerging trends, upcoming technologies, and future opportunities that will shape how we approach ${keyword} in the coming years.`,
          targetKeyword: `future of ${keyword}`
        }
      ],
      inBlogImages: [
        {
          alt: `${keyword} implementation process diagram`,
          title: "Implementation Process",
          description: "Detailed flowchart showing the step-by-step ${keyword} implementation process",
          placement: "after_section_2"
        },
        {
          alt: `${keyword} best practices infographic`,
          title: "Best Practices Overview",
          description: "Visual guide highlighting key ${keyword} best practices and tips",
          placement: "after_section_4"
        }
      ],
      conclusion: `Mastering ${keyword} is essential for success in today's competitive solar industry. By implementing the strategies and best practices outlined in this guide, you'll be well-equipped to deliver exceptional results for your clients. ${companyData.companyName} is here to support your journey with professional services and expert guidance. Contact us today to learn how we can help you excel in ${keyword} and take your solar projects to the next level.`
    };
  }
}

module.exports = new BlogController();