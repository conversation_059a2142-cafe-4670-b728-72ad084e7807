const { sheets } = require('../config/google.config');

class GoogleSheetsService {
  async getCompanyData() {
    try {
      console.log('Fetching company data from sheet:', process.env.COMPANY_KT_SHEET_ID);
      
      const response = await sheets.spreadsheets.values.get({
        spreadsheetId: process.env.COMPANY_KT_SHEET_ID,
        range: 'Wattmonk KT!A:D',
        key: process.env.GOOGLE_SHEETS_API_KEY
      });

      const rows = response.data.values;
      if (!rows || rows.length === 0) {
        console.log('No data found in sheet');
        return [];
      }

      console.log(`Found ${rows.length} rows`);

      // Skip header row and map data
      const companies = rows.slice(1).map((row, index) => ({
        id: `company_${index + 1}`,
        companyName: row[0] || '',
        servicesOffered: row[1] || '',
        serviceOverview: row[2] || '',
        aboutTheCompany: row[3] || ''
      }));

      // Filter out empty rows
      return companies.filter(company => company.companyName);
    } catch (error) {
      console.error('Error fetching company data:', error.message);
      // Return test data if Google Sheets fails
      return [{
        id: 'company_1',
        companyName: 'Wattmonk',
        servicesOffered: 'Solar Sales Proposal, Site Survey, Plan Set',
        serviceOverview: 'Professional solar services',
        aboutTheCompany: 'Leading solar service provider'
      }];
    }
  }

  async getManualKeywords() {
    try {
      console.log('Fetching keywords from sheet:', process.env.WATTMONK_BLOG_SHEET_ID);
      
      const response = await sheets.spreadsheets.values.get({
        spreadsheetId: process.env.WATTMONK_BLOG_SHEET_ID,
        range: 'Manual Keywords!A:E',
        key: process.env.GOOGLE_SHEETS_API_KEY
      });

      const rows = response.data.values;
      if (!rows || rows.length === 0) {
        return [];
      }

      // Skip header and map data
      const keywords = rows.slice(1).map((row, index) => ({
        id: `keyword_${index + 1}`,
        focusKeyword: row[0] || '',
        articleFormat: row[1] || '',
        wordCount: row[2] || '',
        targetAudience: row[3] || '',
        objective: row[4] || ''
      }));

      return keywords.filter(keyword => keyword.focusKeyword);
    } catch (error) {
      console.error('Error fetching manual keywords:', error.message);
      // Return test data if Google Sheets fails
      return [
        {
          id: 'keyword_1',
          focusKeyword: 'solar PTO',
          articleFormat: 'How To',
          wordCount: '1200',
          targetAudience: 'Solar Installers',
          objective: 'Promote Solar PTO Service'
        }
      ];
    }
  }

  async getCompanyByName(companyName) {
    const companies = await this.getCompanyData();
    return companies.find(company => 
      company.companyName.toLowerCase() === companyName.toLowerCase()
    );
  }
   async getAlignedBlogData(focusKeyword) {
    try {
      const response = await sheets.spreadsheets.values.get({
        spreadsheetId: process.env.WATTMONK_BLOG_SHEET_ID,
        range: 'Manual Keywords!A:E',
        key: process.env.GOOGLE_SHEETS_API_KEY
      });

      const rows = response.data.values;
      if (!rows || rows.length === 0) return null;

      // Find the row with matching focus keyword
      const keywordRow = rows.find(row =>
        row[0]?.toLowerCase() === focusKeyword.toLowerCase()
      );

      if (keywordRow) {
        return {
          focusKeyword: keywordRow[0],
          articleFormat: keywordRow[1],
          wordCount: keywordRow[2],
          targetAudience: keywordRow[3],
          objective: keywordRow[4]
        };
      }

      return null;
    } catch (error) {
      console.error('Error fetching aligned blog data:', error);
      // Return fallback data instead of throwing error
      return {
        focusKeyword: focusKeyword,
        articleFormat: 'How To',
        wordCount: '2000',
        targetAudience: 'Solar Installers',
        objective: 'Promote Solar Services'
      };
    }
  }
}

module.exports = new GoogleSheetsService();