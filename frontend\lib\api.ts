const API_BASE_URL = "http://localhost:5000/api"

class ApiClient {
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`

    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      ...options,
    })

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`)
    }

    return response.json()
  }

  // Companies
  async getCompanies() {
    return this.request("/company")
  }

  // Blog workflow
  async startBlog(companyName: string) {
    return this.request("/blog/start", {
      method: "POST",
      body: JSON.stringify({ companyName }),
    })
  }

  async selectKeywordAnalyze(draftId: string, selectedKeyword: string) {
    return this.request("/blog/select-keyword-analyze", {
      method: "POST",
      body: JSON.stringify({ draftId, selectedKeyword }),
    })
  }

  async generateMetaScores(draftId: string) {
    return this.request("/blog/generate-meta-scores", {
      method: "POST",
      body: JSON.stringify({ draftId }),
    })
  }

  async selectMeta(draftId: string, selectedMetaIndex: number) {
    return this.request("/blog/select-meta", {
      method: "POST",
      body: JSON.stringify({ draftId, selectedMetaIndex }),
    })
  }

  async generateStructuredContent(draftId: string) {
    return this.request("/blog/generate-structured-content", {
      method: "POST",
      body: JSON.stringify({ draftId }),
    })
  }

  async regenerateBlock(
    draftId: string,
    blockId: string,
    regenerationType: "ai" | "manual",
    customPrompt?: string,
    newContent?: string,
  ) {
    return this.request("/blog/regenerate-block", {
      method: "POST",
      body: JSON.stringify({ draftId, blockId, regenerationType, customPrompt, newContent }),
    })
  }

  async generateLinks(draftId: string) {
    return this.request("/blog/generate-links", {
      method: "POST",
      body: JSON.stringify({ draftId }),
    })
  }

  async deployWordPress(draftId: string) {
    return this.request("/blog/deploy-wordpress", {
      method: "POST",
      body: JSON.stringify({ draftId }),
    })
  }

  // Draft management
  async getDraft(draftId: string) {
    return this.request(`/blog/draft/${draftId}`)
  }

  async listDrafts() {
    return this.request("/blog/drafts")
  }

  // WordPress
  async testWordPress() {
    return this.request("/blog/test-wordpress")
  }
}

export const api = new ApiClient()
