class CitationService {
  constructor() {
    // Trusted sources for citations (non-competitor)
    this.trustedSources = [
      { name: 'SEIA', url: 'https://www.seia.org/', type: 'Industry Association' },
      { name: 'NREL', url: 'https://www.nrel.gov/', type: 'Research Institute' },
      { name: 'Energy.gov', url: 'https://www.energy.gov/', type: 'Government' },
      { name: 'IEA', url: 'https://www.iea.org/', type: 'International Organization' },
      { name: 'PV Magazine', url: 'https://www.pv-magazine.com/', type: 'Industry Publication' }
    ];
  }

  async generateCitations(keyword, content) {
    try {
      const { model } = require('../config/gemini.config');
      const prompt = `
        For a blog about "${keyword}", suggest 5 relevant citations from these trusted sources:
        ${this.trustedSources.map(s => `${s.name} (${s.type})`).join(', ')}
        
        Base on content topics: ${content}
        
        Return in JSON format:
        {
          "citations": [
            {
              "source": "source name",
              "title": "article/report title",
              "url": "full URL",
              "relevance": "why this citation is relevant",
              "statistic": "key statistic or fact to cite"
            }
          ]
        }
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
            try {
        const cleanedText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
        const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } catch (parseError) {
        console.error('Citation parsing error:', parseError);
      }

      // Fallback citations
      return {
        citations: [
          {
            source: "SEIA",
            title: "Solar Industry Research Data",
            url: "https://www.seia.org/solar-industry-research-data",
            relevance: "Industry statistics and growth trends",
            statistic: "Solar has experienced an average annual growth rate of 24% over the last decade"
          },
          {
            source: "NREL",
            title: "Solar Technology Cost Analysis",
            url: "https://www.nrel.gov/solar/solar-costs.html",
            relevance: "Cost reduction trends and efficiency improvements",
            statistic: "Solar panel costs have dropped by 90% since 2010"
          },
          {
            source: "Energy.gov",
            title: "Solar Energy Technologies Office",
            url: "https://www.energy.gov/eere/solar/solar-energy-technologies-office",
            relevance: "Government programs and incentives",
            statistic: "Federal tax credit covers 30% of solar installation costs"
          }
        ]
      };
    } catch (error) {
      console.error('Error generating citations:', error);
      throw error;
    }
  }

  formatCitationForWordPress(citation, index) {
    // Format citation as WordPress-compatible HTML with proper linking
    return `<sup><a href="${citation.url}" target="_blank" rel="noopener noreferrer">[${index + 1}]</a></sup>`;
  }

  generateReferenceSection(citations) {
    // Create a properly formatted references section for WordPress
    let references = '<h2>References</h2>\n<ol>\n';
    citations.forEach((citation, index) => {
      references += `<li>${citation.source}. "${citation.title}." Available at: <a href="${citation.url}" target="_blank" rel="noopener noreferrer">${citation.url}</a></li>\n`;
    });
    references += '</ol>';
    return references;
  }
}

module.exports = new CitationService();