// Temporary in-memory storage
const drafts = new Map();

class DraftService {
  async createDraft(data) {
    try {
      const draftId = `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const draft = {
        ...data,
        id: draftId,
        status: data.status || 'draft',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      drafts.set(draftId, draft);
      console.log(`Draft created: ${draftId}`);
      return draft;
    } catch (error) {
      console.error('Error creating draft:', error);
      throw error;
    }
  }

  async getDraft(draftId) {
    try {
      const draft = drafts.get(draftId);
      if (!draft) {
        throw new Error('Draft not found');
      }
      return draft;
    } catch (error) {
      console.error('Error getting draft:', error);
      throw error;
    }
  }

  async updateDraft(draftId, updates) {
    try {
      const draft = drafts.get(draftId);
      if (!draft) {
        throw new Error('Draft not found');
      }

      const updatedDraft = {
        ...draft,
        ...updates,
        id: draftId,
        updatedAt: new Date().toISOString()
      };

      drafts.set(draftId, updatedDraft);
      console.log(`Draft updated: ${draftId}`);
      return updatedDraft;
    } catch (error) {
      console.error('Error updating draft:', error);
      throw error;
    }
  }

  async listDrafts(filters = {}) {
    try {
      let draftList = Array.from(drafts.values());
      
      if (filters.companyId) {
        draftList = draftList.filter(d => d.companyId === filters.companyId);
      }
      if (filters.status) {
        draftList = draftList.filter(d => d.status === filters.status);
      }

      return draftList.sort((a, b) => 
        new Date(b.createdAt) - new Date(a.createdAt)
      );
    } catch (error) {
      console.error('Error listing drafts:', error);
      throw error;
    }
  }
}

module.exports = new DraftService();